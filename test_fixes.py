#!/usr/bin/env python3
"""
Test Script for ML Log Prediction Fixes

This script tests all the fixes applied to resolve the issues in d_error_message.md:
1. PyPOTS availability check
2. Function signature compatibility
3. Sequence creation robustness
4. Fallback implementation metadata
"""

import sys
import os
import traceback

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_tensorflow_and_pypots():
    """Test TensorFlow and PyPOTS availability."""
    print("🧪 Testing TensorFlow and PyPOTS availability...")
    
    try:
        # Test TensorFlow
        import tensorflow as tf
        print(f"✅ TensorFlow {tf.__version__} loaded successfully")
        
        # Test PyPOTS
        import pypots
        print(f"✅ PyPOTS {pypots.__version__} loaded successfully")
        
        # Test compatibility module
        from utils.tensorflow_compatibility import import_pypots_safe
        success, modules, error = import_pypots_safe(force_recheck=True)
        
        if success:
            print("✅ PyPOTS compatibility module working")
            print(f"   Available modules: {list(modules.keys()) if modules else 'None'}")
        else:
            print(f"❌ PyPOTS compatibility issue: {error}")
            
        return True
        
    except Exception as e:
        print(f"❌ TensorFlow/PyPOTS test failed: {e}")
        traceback.print_exc()
        return False

def test_saits_model_creation():
    """Test SAITS model creation with dynamic PyPOTS check."""
    print("\n🧪 Testing SAITS model creation...")
    
    try:
        from models.advanced_models.saits_model import SAITSModel
        
        # Try to create a SAITS model
        model = SAITSModel(
            n_features=4,
            sequence_len=32,  # Use smaller sequence length
            epochs=1,  # Minimal epochs for testing
            batch_size=16
        )
        
        print("✅ SAITS model created successfully")
        print(f"   Model type: {type(model)}")
        print(f"   Features: {model.n_features}")
        print(f"   Sequence length: {model.sequence_len}")
        
        return True
        
    except Exception as e:
        print(f"❌ SAITS model creation failed: {e}")
        traceback.print_exc()
        return False

def test_function_signature():
    """Test function signature compatibility."""
    print("\n🧪 Testing function signature compatibility...")
    
    try:
        # Test the function signature check
        from preprocessing.deep_model.phase1_integration import impute_logs_deep_phase1_safe
        import inspect
        
        sig = inspect.signature(impute_logs_deep_phase1_safe)
        params = list(sig.parameters.keys())
        
        print(f"✅ Function signature check successful")
        print(f"   Parameters: {params}")
        
        # Check if optimization_level is supported
        if 'optimization_level' in params:
            print("✅ optimization_level parameter supported")
        else:
            print("⚠️ optimization_level parameter not found")
            
        return True
        
    except Exception as e:
        print(f"❌ Function signature test failed: {e}")
        traceback.print_exc()
        return False

def test_sequence_creation():
    """Test sequence creation with small dataset."""
    print("\n🧪 Testing sequence creation robustness...")
    
    try:
        import pandas as pd
        import numpy as np
        from core_code.data_handler import create_sequences
        
        # Create a small test dataset
        np.random.seed(42)
        test_data = {
            'WELL': ['W1'] * 50 + ['W2'] * 50,
            'DEPTH': list(range(50)) + list(range(50)),
            'GR': np.random.normal(50, 10, 100),
            'RHOB': np.random.normal(2.3, 0.2, 100),
            'NPHI': np.random.normal(0.15, 0.05, 100),
            'RT': np.random.lognormal(1, 1, 100)
        }
        
        df = pd.DataFrame(test_data)
        feature_cols = ['GR', 'RHOB', 'NPHI', 'RT']
        
        # Test with different sequence lengths
        for seq_len in [32, 16, 8]:
            print(f"   Testing sequence length: {seq_len}")
            
            sequences, metadata = create_sequences(
                df, 'WELL', feature_cols, 
                sequence_len=seq_len, 
                use_enhanced=True
            )
            
            if sequences.size > 0:
                print(f"   ✅ Created {sequences.shape[0]} sequences with length {seq_len}")
                break
            else:
                print(f"   ⚠️ No sequences created with length {seq_len}")
        
        if sequences.size > 0:
            print("✅ Sequence creation test successful")
            return True
        else:
            print("❌ No sequences could be created")
            return False
            
    except Exception as e:
        print(f"❌ Sequence creation test failed: {e}")
        traceback.print_exc()
        return False

def test_metadata_structure():
    """Test that metadata has required structure."""
    print("\n🧪 Testing metadata structure...")
    
    try:
        # Test the metadata structure that was causing the 'missing_rate_before' error
        test_metadata = {
            'reports': {
                'validation': {'data_quality_score': 0.8},
                'encoding': {'missing_rate_before': 0.0, 'missing_rate_after': 0.0}
            },
            'final_stability': {'is_stable': True}
        }
        
        # Test accessing the problematic keys
        quality_score = test_metadata['reports']['validation']['data_quality_score']
        missing_before = test_metadata['reports']['encoding']['missing_rate_before']
        missing_after = test_metadata['reports']['encoding']['missing_rate_after']
        is_stable = test_metadata['final_stability']['is_stable']
        
        print("✅ Metadata structure test successful")
        print(f"   Quality score: {quality_score}")
        print(f"   Missing rate: {missing_before:.1%} → {missing_after:.1%}")
        print(f"   Stability: {'✅ STABLE' if is_stable else '❌ UNSTABLE'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Metadata structure test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Running ML Log Prediction Fix Tests")
    print("=" * 50)
    
    tests = [
        ("TensorFlow & PyPOTS", test_tensorflow_and_pypots),
        ("SAITS Model Creation", test_saits_model_creation),
        ("Function Signature", test_function_signature),
        ("Sequence Creation", test_sequence_creation),
        ("Metadata Structure", test_metadata_structure)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("🏁 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! The fixes should resolve the issues.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the output above.")
        return 1

if __name__ == "__main__":
    exit(main())
