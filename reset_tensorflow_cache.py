#!/usr/bin/env python3
"""
Reset TensorFlow Cache Script

This script resets the TensorFlow compatibility cache and tests if TensorFlow
is now working properly after the NumPy fix.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("🔄 Resetting TensorFlow cache and testing...")
    
    try:
        # Import and reset the cache
        from utils.tensorflow_compatibility import import_tensorflow_safe, reset_tensorflow_cache
        
        print("1. Resetting TensorFlow cache...")
        reset_tensorflow_cache()
        
        print("2. Testing TensorFlow import with force recheck...")
        success, tf_module, error = import_tensorflow_safe(force_recheck=True)
        
        if success:
            print(f"✅ TensorFlow loaded successfully!")
            print(f"   Version: {tf_module.__version__}")
            print(f"   GPU Available: {tf_module.test.is_gpu_available() if hasattr(tf_module.test, 'is_gpu_available') else 'Unknown'}")
        else:
            print(f"❌ TensorFlow failed to load: {error}")
            
        print("3. Testing PyPOTS availability...")
        try:
            import pypots
            print(f"✅ PyPOTS loaded successfully!")
            print(f"   Version: {pypots.__version__}")
        except ImportError as e:
            print(f"❌ PyPOTS failed to load: {e}")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return 1
        
    return 0

if __name__ == "__main__":
    exit(main())
