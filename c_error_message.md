==================================================
🔍 ENVIRONMENT DIAGNOSTICS
==================================================
✅ MSVC: MSVC compiler available
❌ Windows SDK: Not detected
   💡 Install Windows SDK for development headers
✅ CUDA: CUDA toolkit detected: Cuda compilation tools, release 11.8, V11.8.89

📋 Summary:
   ✅ Some development tools available
==================================================
   [INFO] Environment already initialized
[PHASE1] Configuring memory optimization environment...
   [OK] PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
Multiple Linear Regression utilities loaded successfully
Data leakage detection module loaded
Loading advanced deep learning models...
🔧 TensorFlow environment configured for compatibility
⚠️ TensorFlow DLL loading failed - this is a common Windows issue
💡 Suggestions:
   1. Install Microsoft Visual C++ Redistributable
   2. Use tensorflow-cpu instead of tensorflow
   3. Update to compatible numpy version
   4. Run: python fix_tensorflow_dll.py
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk3\utils\tensorflow_compatibility.py:212: UserWarning: TensorFlow initialization failed: Traceback (most recent call last):
  File "C:\Users\<USER>\mwlt\lib\site-packages\tensorflow\python\pywrap_tensorflow.py", line 73, in <module>
    from tensorflow.python._pywrap_tensorflow_internal import *
ImportError: DLL load failed while importing _pywrap_tensorflow_internal: A dynamic link library (DLL) initialization routine failed.


Failed to load the native TensorFlow runtime.
See https://www.tensorflow.org/install/errors for some common causes and solutions.
If you need help, create an issue at https://github.com/tensorflow/tensorflow/issues and include the entire stack trace above this error message.
  warnings.warn(f"TensorFlow initialization failed: {tf_error}")
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk3\models\advanced_models\saits_model.py:30: UserWarning: PyPOTS not available: PyPOTS requires TensorFlow: TensorFlow not available (cached result)
  warnings.warn(f"PyPOTS not available: {pypots_error}")
SAITS model loaded successfully
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk3\models\advanced_models\brits_model.py:30: UserWarning: PyPOTS not available: PyPOTS requires TensorFlow: TensorFlow not available (cached result)
  warnings.warn(f"PyPOTS not available: {pypots_error}")
BRITS model loaded successfully
Advanced models loaded: ['saits', 'brits']
Total available: 2/5 models
Advanced models module initialized (Phase 1 foundation)
Ready for Phase 2: Core model implementations
Advanced deep learning models module loaded
Available advanced models: ['saits', 'brits']
SAITS model added to registry
BRITS model added to registry
Enhanced MODEL_REGISTRY with 2 advanced models
Available advanced models: ['saits', 'brits']
   [OK] Phase 1 Enhanced Deep Learning Integration loaded
   [OK] Optimized pipeline functions available
INFO:utils.display_utils:Configuring fonts for Windows system
INFO:utils.display_utils:Set matplotlib font to: Segoe UI Emoji
INFO:utils.display_utils:Emoji support confirmed
INFO:utils.display_utils:Configured warning filters for font issues
⚠️ CUDA not available, all operations will use CPU
🔍 Performance Monitor initialized
   • Monitoring interval: 1.0s
[MEM] Environment configured for memory optimization
[MEM] Memory Optimizer initialized
   * Mixed precision enabled
   * Memory monitoring enabled
   [OK] Memory optimizer initialized
============================================================
 ML LOG PREDICTION
============================================================

[MEM] Initial Memory Status:

==================================================
[MEM] MEMORY STATUS
==================================================
System Memory:
   * Total: 31.7 GB
   * Available: 14.4 GB
   * Usage: 54.5%
==================================================

Step 1: Select LAS files
Select LAS files using the file dialog...
Selected 8 LAS files:
  1. B-G-6_RP_INPUT.las
  2. B-G-10_RP_INPUT.las
  3. B-L-1_RP_INPUT.las
  4. B-L-2.G1_RP_INPUT.las
  5. B-L-6_RP_INPUT.las
  6. B-L-9_RP_INPUT.las
  7. B-L-15_RP_INPUT.las
  8. EB-1_RP_INPUT.las

Step 2: Loading LAS files...
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-G-6_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-G-6
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-G-10_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-G-10
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-L-1_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-L-1
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-L-2.G1_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-L-2.G1
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-L-6_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-L-6
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-L-9_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-L-9
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-L-15_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-L-15
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/EB-1_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded EB-1
Successfully loaded:
   • 57856 data points
   • 8 wells: B-G-10, B-G-6, B-L-1, B-L-15, B-L-2.G1, B-L-6, B-L-9, EB-1
   • 41 log curves: CALI, DENS_WET, DT, DTS, FACIES_INTERPRETATION_2021, FLUID_CODE, FLUID_REINTERPRETATION_2021, GDRY, GR, GSAT, GSOLID, KDRY, KFLUID, KSAT, KSAT_WET, KSOLID, LITHO_CODE, NPHI, P-IMPEDANCE_TRANS, P-WAVE, PHID, PHIE, PHIE_COREL, PHIT, POISSON'S_RATIO_TRANS, PVEL_WET, RHOB, RHOFLUID, RHOSOLID, RT, S-IMPEDANCE_TRANS, S-WAVE, SEIS_DEPTH, SQ_VSVP_RATIO, SVEL_WET, SWE, SWT, TVDSS, VOL_WETCLAY, VPVS_RATIO_TRANS, VSVP_RATIO_TRANS

Step 3: Configure feature and target logs

Select feature logs (comma separated indexes)
  1. CALI
  2. DENS_WET
  3. DT
  4. DTS
  5. FACIES_INTERPRETATION_2021
  6. FLUID_CODE
  7. FLUID_REINTERPRETATION_2021
  8. GDRY
  9. GR
  10. GSAT
  11. GSOLID
  12. KDRY
  13. KFLUID
  14. KSAT
  15. KSAT_WET
  16. KSOLID
  17. LITHO_CODE
  18. NPHI
  19. P-IMPEDANCE_TRANS
  20. P-WAVE
  21. PHID
  22. PHIE
  23. PHIE_COREL
  24. PHIT
  25. POISSON'S_RATIO_TRANS
  26. PVEL_WET
  27. RHOB
  28. RHOFLUID
  29. RHOSOLID
  30. RT
  31. S-IMPEDANCE_TRANS
  32. S-WAVE
  33. SEIS_DEPTH
  34. SQ_VSVP_RATIO
  35. SVEL_WET
  36. SWE
  37. SWT
  38. TVDSS
  39. VOL_WETCLAY
  40. VPVS_RATIO_TRANS
  41. VSVP_RATIO_TRANS
Default: ['CALI', 'DENS_WET', 'DT', 'DTS']
Selection: 9, 18, 27, 30, 38

Select target log
  1. CALI
  2. DENS_WET
  3. DT
  4. DTS
  5. FACIES_INTERPRETATION_2021
  6. FLUID_CODE
  7. FLUID_REINTERPRETATION_2021
  8. GDRY
  9. GSAT
  10. GSOLID
  11. KDRY
  12. KFLUID
  13. KSAT
  14. KSAT_WET
  15. KSOLID
  16. LITHO_CODE
  17. P-IMPEDANCE_TRANS
  18. P-WAVE
  19. PHID
  20. PHIE
  21. PHIE_COREL
  22. PHIT
  23. POISSON'S_RATIO_TRANS
  24. PVEL_WET
  25. RHOFLUID
  26. RHOSOLID
  27. S-IMPEDANCE_TRANS
  28. S-WAVE
  29. SEIS_DEPTH
  30. SQ_VSVP_RATIO
  31. SVEL_WET
  32. SWE
  33. SWT
  34. VOL_WETCLAY
  35. VPVS_RATIO_TRANS
  36. VSVP_RATIO_TRANS
Default: CALI
Selection: 18
Feature logs: GR, NPHI, RHOB, RT, TVDSS
Target log: P-WAVE

Step 4: Configure training/prediction strategy

Training/prediction mode?
  1. mixed
  2. separated
Default: mixed
Selection: 1
Mode: mixed

Step 5: Configure prediction mode

Prediction mode: 1 fill-missing, 2 CV, 3 full
  1. 1
  2. 2
  3. 3
Default: 1
Selection: 3
Prediction mode: 3

Step 6: Configure deep learning pipeline optimization

🚀 Deep Learning Pipeline Configuration
============================================================
Choose the training pipeline for SAITS/BRITS models:

1. 🔥 Optimized Phase 1 Pipeline (RECOMMENDED)
   • 3-4x faster training with moderate optimization
   • Advanced preprocessing and validation
   • GPU-accelerated operations when available
   • Automatic fallback to original pipeline if issues occur

2. 📚 Original Training Pipeline
   • Standard training path from ml_core.py
   • Proven stability and compatibility
   • No additional optimizations

3. ⚡ Maximum Performance Pipeline (EXPERIMENTAL)
   • 4-6x faster training with aggressive optimization
   • GPU preprocessing and tensor operations
   • Best for large datasets and modern hardware

Select pipeline (1-3) [1]: 3

⚠️ IMPORTANT: Maximum Performance Pipeline Requirements
   • Best for datasets with >1000 rows and >50 rows per well
   • May auto-adjust to moderate optimization for small datasets
   • Includes automatic fallback mechanisms
   • GPU acceleration enabled when available

Continue with Maximum Performance Pipeline? (y/n) [y]: y
💻 CUDA not available - using CPU optimizations

✅ Pipeline Configuration:
   • Pipeline: Maximum Performance
   • Expected speedup: 4-6x
   • GPU strategy: cpu

Step 7: Configure model hyperparameters
   💻 saits: Using CPU optimizations
   💻 brits: Using CPU optimizations
✅ Hyperparameters configured with GPU optimizations

Step 8: Data cleaning and quality control

Coverage:
  GR: 89.8%
  NPHI: 73.8%
  RHOB: 63.7%
  RT: 82.4%
  TVDSS: 95.6%
  P-WAVE: 65.3%

Step 8.5: Data sufficiency optimization

📊 Data Sufficiency Analysis:
   • Total wells: 8
   • Median well size: 7832 rows
   • Small wells (< 50 rows): 0.0%
   • Optimal sequence length: 64
✅ Hyperparameters optimized for data sufficiency

Step 9: Running machine learning models...

Model selection options:
• Select multiple models by entering comma-separated numbers (e.g., 1,2,3)
• Type 'all' to select all available models
• Press Enter for default single model selection

📋 Well Log Model Selection Guide:
   Primary Imputation Models (filling missing values in existing sequences):
   1. SAITS - Best for complex geological patterns with long-range dependencies
   2. BRITS - Optimal for maintaining bidirectional geological continuity

   Primary Prediction Models (predicting log values in new wells):
   1. XGBoost - Excellent feature-target learning for cross-well prediction
   2. LightGBM - Fast and accurate for large-scale well log datasets
   3. CatBoost - Robust handling of mixed geological data types
   4. Transformer - Captures complex geological relationships across wells

   Versatile Models (both imputation and prediction):
   1. Transformer - Attention mechanism adapts to both tasks
   2. mRNN - Multi-scale processing handles both local and regional tasks
   3. Gradient Boosting Models - Consistent performance across both scenarios

Select model(s) to run
  1. xgboost
  2. lightgbm
  3. catboost
  4. linear_regression
  5. ridge_regression
  6. lasso_regression
  7. elastic_net
  8. saits
  9. brits
Default: ['xgboost']
Selection: 8
Selected models: saits

Running 1 model(s)...

--- Running Model 1/1: saits ---
🚀 [MAXIMUM PERFORMANCE] Starting optimized training for saits
   Expected speedup: 4-6x
   Optimization level: aggressive
   GPU strategy: cpu
[MEM] Applying memory optimization with Maximum Performance
[MEM] Memory cleared
[MEM] Memory cleared
saits failed with error: impute_logs_deep_phase1_safe() got an unexpected keyword argument 'optimization_level'

Batch execution summary:
   • Successful models: 0
   • Failed models: 1
   • Failed: saits
All models failed. Continuing to next step...

Step 10: Configure output options
No successful models to process. Skipping to next step...
