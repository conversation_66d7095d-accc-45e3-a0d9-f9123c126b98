# SAITS/BRITS Reconstruction and Re-Assembly Fix Plan (GPU3 RK3)

This document details two implementation options to fix reconstruction failures and improve robustness in the SAITS/BRITS pipeline within this codebase. It focuses on the post-processing and re-assembly phase where predictions are written back to the full-frame DataFrame.

Goals:
- Eliminate index-space mismatches during reconstruction
- Improve numerical stability (dtype, NaN/Inf handling)
- Enforce strict shape/feature alignment
- Strengthen diagnostics and fallbacks

---

## Option A — Use label-based indices consistently (preferred)

Principle: Make the reconstruction accumulators (pred_sum_df, pred_count_df) share the SAME index labels as the DataFrame used to create sequences so that metadata['original_indices'] can be used directly with .loc.

Where to change:
- core_code/ml_core.py (reconstruction/assembly section)
- core_code/data_handler.py (clarify that metadata['original_indices'] are label indices from the source DataFrame)
- models/advanced_models/base_model.py (ensure predictions are aligned with metadata order)

Implementation steps:
1) Build accumulators with source labels and safer dtypes:
   - pred_sum_df = pd.DataFrame(0.0, index=source_df.index, columns=target_cols, dtype=np.float32)
   - pred_count_df = pd.DataFrame(0.0, index=source_df.index, columns=target_cols, dtype=np.float32)

2) Pre-flight validation before reconstruction loop:
   - Ensure every sequence’s original_indices ⊆ pred_sum_df.index
   - If any missing labels found, log first N missing and raise a clear error with remediation (likely indicates upstream filtering/reindexing mismatch)

3) Prediction shape and dtype guards:
   - Assert predicted.shape == (len(original_indices), len(target_cols))
   - Convert to np.float32 and check np.isfinite(predicted).all(); if not, replace invalid with 0 and mark a mask of valid positions per element

4) Vectorized accumulation using .loc with a validity mask:
   - Use .loc[original_indices, target_cols] += predicted_valid
   - Increment pred_count_df only where valid cells exist: count += valid_mask.astype(np.float32)

5) Safe averaging:
   - final = np.divide(pred_sum_df.values, pred_count_df.values, out=np.zeros_like(pred_sum_df.values, dtype=np.float32), where=(pred_count_df.values > 0))
   - Wrap back into DataFrame with same index/columns

6) Inverse transform and column alignment:
   - If inverse_transform is applied, ensure shape remains (N, len(target_cols)); select ordered target_cols after inverse transform.

7) Strengthen logging/diagnostics:
   - At start: log index type, index min/max, sample of original_indices, predicted shapes for first 3 sequences
   - If vectorized write fails (shouldn’t with checks), provide targeted debug: sequence_id, first 5 indices, shapes, dtype

8) Batch alignment check:
   - After predict or predict_large_dataset, confirm the number of sequences predicted equals metadata length and same order (e.g., zip(predictions, metadata_seq))

Pros:
- Minimal conceptual change; uses existing metadata labels directly
- Clear error messages when upstream misalignment happens
- Robust to DataFrame not starting at 0 or having gaps in labels

Cons:
- Requires accumulator creation from the exact source DataFrame used at sequence creation time

---

## Option B — Map labels to positions and use .iloc consistently

Principle: Treat metadata['original_indices'] as label indices, but convert to positional indices of the target DataFrame before reconstruction; then write using .iloc.

Where to change:
- core_code/ml_core.py (reconstruction/assembly section)

Implementation steps:
1) Build a one-time label->position map:
   - index_map = {label: pos for pos, label in enumerate(target_df.index)}

2) For each sequence, map labels to positions:
   - pos = [index_map[i] for i in original_indices if i in index_map]
   - If any label missing, log and skip those positions (or fail fast based on strictness setting)

3) Create float32 accumulators with positional alignment:
   - pred_sum = np.zeros((len(target_df.index), len(target_cols)), dtype=np.float32)
   - pred_count = np.zeros_like(pred_sum, dtype=np.float32)

4) Prediction guards (shape, dtype, finiteness): same as Option A

5) Accumulate via positions:
   - pred_sum[pos, :] += predicted_valid
   - pred_count[pos, :] += valid_mask.astype(np.float32)

6) Safe averaging and DataFrame re-wrap:
   - final = np.divide(pred_sum, pred_count, out=np.zeros_like(pred_sum, dtype=np.float32), where=(pred_count > 0))
   - final_df = pd.DataFrame(final, index=target_df.index, columns=target_cols)

7) Inverse transform and column alignment: same as Option A

8) Diagnostics:
   - Log count of unmapped labels; if >0, provide top K examples and guidance to ensure target_df comes from same source or to synchronize filtering/sorting steps

Pros:
- Decouples reconstruction from strict label matching
- Potentially faster with pure numpy accumulation

Cons:
- Adds a mapping step and positional write logic
- If the pipeline changes sorting, you must rebuild the map accordingly

---

## Cross-cutting safeguards (apply to both options)

- Stop using float16 in accumulators; use float32 for sums and counts to avoid overflow/underflow and dtype coercion issues.
- Division safety: Always use where=count>0 to avoid NaN/Inf; post-check that final has finite values or report which rows/cols are invalid.
- Sequence/prediction alignment: After batching, verify the number of prediction sequences equals the metadata count and sequence order is consistent; add a hard assert with a clear message.
- Feature alignment: Ensure len(target_cols) equals prediction last dimension. If inverse_transform returns a wider array, reindex to target_cols in order before writing.
- Fallback logic: If vectorized write is attempted, run pre-checks (index existence and shapes). The element-wise fallback should also validate index existence before setting values; otherwise, skip and log.
- End-to-end small test: Build a tiny flow (one well, few hundred rows), run reconstruction, and validate: no exceptions, no NaNs in averaged output where counts>0, visible alignment of a window.

---

## Code touchpoints and suggested patch locations

- core_code/ml_core.py
  - Reconstruction accumulator initialization: ensure index/shape match and dtype float32
  - Reconstruction loop: add pre-checks for index existence; switch to .loc (Option A) or iloc with mapped positions (Option B)
  - Averaging step: safe divide with where>0
  - Logging: detailed sequence-level diagnostics (only for first few sequences to limit noise)

- core_code/data_handler.py
  - Document that metadata['original_indices'] are label indices from the source DataFrame used at sequence creation time
  - If enhanced sequence creation sometimes returns filtered sets, ensure the reconstruction target uses the same base DataFrame/index or expose a mapping utility

- models/advanced_models/base_model.py
  - After predict_large_dataset/predict, assert prediction count matches metadata length and ordering; otherwise, raise with actionable message

---

## Migration and rollout plan

1) Implement Option A (preferred) behind a config flag (e.g., reconstruction.index_mode = "label"|"position") defaulting to "label".
2) Add diagnostics and assertions; run test_saits_execution.py and a small end-to-end sample.
3) If data sources with incompatible indices are common, implement Option B and allow switching via config.
4) Monitor logs for missing index mapping or shape mismatches; address upstream filtering/sorting inconsistencies.

---

## Acceptance criteria
- No more "Error processing sequence"/"Fallback also failed" during reconstruction
- No NaNs/Inf in final averaged predictions for covered rows
- Predictions align with expected rows/columns after inverse transform
- Batch prediction counts match metadata counts; no silent drops

---

## Troubleshooting checklist
- Do pred_sum_df/pred_count_df share the same index labels as the DataFrame used in sequence creation?
- Are metadata['original_indices'] labels or positions? Are we using the correct API (.loc or .iloc) consistently?
- Do predicted arrays have shape (seq_len, n_features) exactly matching target columns? After inverse_transform too?
- Are accumulators float32, and is division done with where=count>0?
- Any missing mappings from labels to positions (Option B)?
- Does batch output length equal metadata length and preserve order?

---

By following Option A or Option B with the safeguards above, reconstruction will be deterministic, stable, and free of index/dtype-related failures, while remaining efficient and traceable.