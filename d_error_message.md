   [OK] Memory optimizer initialized
============================================================
 ML LOG PREDICTION
============================================================

[MEM] Initial Memory Status:

==================================================
[MEM] MEMORY STATUS
==================================================
System Memory:
   * Total: 31.7 GB
   * Available: 13.8 GB
   * Usage: 56.4%

GPU Memory:
   * Total: 4.0 GB
   * Allocated: 0.0 GB
   * Reserved: 0.0 GB
   * Free: 4.0 GB
==================================================

Step 1: Select LAS files
Select LAS files using the file dialog...
Selected 8 LAS files:
  1. B-G-6_RP_INPUT.las
  2. B-G-10_RP_INPUT.las
  3. B-L-1_RP_INPUT.las
  4. B-L-2.G1_RP_INPUT.las
  5. B-L-6_RP_INPUT.las
  6. B-L-9_RP_INPUT.las
  7. B-L-15_RP_INPUT.las
  8. EB-1_RP_INPUT.las

Step 2: Loading LAS files...
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-G-6_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-G-6
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-G-10_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-G-10
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-L-1_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-L-1
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-L-2.G1_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-L-2.G1
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-L-6_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-L-6
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-L-9_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-L-9
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-L-15_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-L-15
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/EB-1_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded EB-1
Successfully loaded:
   • 57856 data points
   • 8 wells: B-G-10, B-G-6, B-L-1, B-L-15, B-L-2.G1, B-L-6, B-L-9, EB-1
   • 41 log curves: CALI, DENS_WET, DT, DTS, FACIES_INTERPRETATION_2021, FLUID_CODE, FLUID_REINTERPRETATION_2021, GDRY, GR, GSAT, GSOLID, KDRY, KFLUID, KSAT, KSAT_WET, KSOLID, LITHO_CODE, NPHI, P-IMPEDANCE_TRANS, P-WAVE, PHID, PHIE, PHIE_COREL, PHIT, POISSON'S_RATIO_TRANS, PVEL_WET, RHOB, RHOFLUID, RHOSOLID, RT, S-IMPEDANCE_TRANS, S-WAVE, SEIS_DEPTH, SQ_VSVP_RATIO, SVEL_WET, SWE, SWT, TVDSS, VOL_WETCLAY, VPVS_RATIO_TRANS, VSVP_RATIO_TRANS

Step 3: Configure feature and target logs

Select feature logs (comma separated indexes)
  1. CALI
  2. DENS_WET
  3. DT
  4. DTS
  5. FACIES_INTERPRETATION_2021
  6. FLUID_CODE
  7. FLUID_REINTERPRETATION_2021
  8. GDRY
  9. GR
  10. GSAT
  11. GSOLID
  12. KDRY
  13. KFLUID
  14. KSAT
  15. KSAT_WET
  16. KSOLID
  17. LITHO_CODE
  18. NPHI
  19. P-IMPEDANCE_TRANS
  20. P-WAVE
  21. PHID
  22. PHIE
  23. PHIE_COREL
  24. PHIT
  25. POISSON'S_RATIO_TRANS
  26. PVEL_WET
  27. RHOB
  28. RHOFLUID
  29. RHOSOLID
  30. RT
  31. S-IMPEDANCE_TRANS
  32. S-WAVE
  33. SEIS_DEPTH
  34. SQ_VSVP_RATIO
  35. SVEL_WET
  36. SWE
  37. SWT
  38. TVDSS
  39. VOL_WETCLAY
  40. VPVS_RATIO_TRANS
  41. VSVP_RATIO_TRANS
Default: ['CALI', 'DENS_WET', 'DT', 'DTS']
Selection: 9, 18, 27, 30, 38

Select target log
  1. CALI
  2. DENS_WET
  3. DT
  4. DTS
  5. FACIES_INTERPRETATION_2021
  6. FLUID_CODE
  7. FLUID_REINTERPRETATION_2021
  8. GDRY
  9. GSAT
  10. GSOLID
  11. KDRY
  12. KFLUID
  13. KSAT
  14. KSAT_WET
  15. KSOLID
  16. LITHO_CODE
  17. P-IMPEDANCE_TRANS
  18. P-WAVE
  19. PHID
  20. PHIE
  21. PHIE_COREL
  22. PHIT
  23. POISSON'S_RATIO_TRANS
  24. PVEL_WET
  25. RHOFLUID
  26. RHOSOLID
  27. S-IMPEDANCE_TRANS
  28. S-WAVE
  29. SEIS_DEPTH
  30. SQ_VSVP_RATIO
  31. SVEL_WET
  32. SWE
  33. SWT
  34. VOL_WETCLAY
  35. VPVS_RATIO_TRANS
  36. VSVP_RATIO_TRANS
Default: CALI
Selection: 18
Feature logs: GR, NPHI, RHOB, RT, TVDSS
Target log: P-WAVE

Step 4: Configure training/prediction strategy

Training/prediction mode?
  1. mixed
  2. separated
Default: mixed
Selection: 1
Mode: mixed

Step 5: Configure prediction mode

Prediction mode: 1 fill-missing, 2 CV, 3 full
  1. 1
  2. 2
  3. 3
Default: 1
Selection: 3
Prediction mode: 3

Step 6: Configure deep learning pipeline optimization

🚀 Deep Learning Pipeline Configuration
============================================================
Choose the training pipeline for SAITS/BRITS models:

1. 🔥 Optimized Phase 1 Pipeline (RECOMMENDED)
   • 3-4x faster training with moderate optimization
   • Advanced preprocessing and validation
   • GPU-accelerated operations when available
   • Automatic fallback to original pipeline if issues occur

2. 📚 Original Training Pipeline
   • Standard training path from ml_core.py
   • Proven stability and compatibility
   • No additional optimizations

3. ⚡ Maximum Performance Pipeline (EXPERIMENTAL)
   • 4-6x faster training with aggressive optimization
   • GPU preprocessing and tensor operations
   • Best for large datasets and modern hardware

Select pipeline (1-3) [1]: 3

⚠️ IMPORTANT: Maximum Performance Pipeline Requirements
   • Best for datasets with >1000 rows and >50 rows per well
   • May auto-adjust to moderate optimization for small datasets
   • Includes automatic fallback mechanisms
   • GPU acceleration enabled when available

Continue with Maximum Performance Pipeline? (y/n) [y]: y

🎯 GPU Detected: NVIDIA T550 Laptop GPU
   Compute Capability: 7.5
   ✅ Mixed precision training supported
   ✅ GPU preprocessing enabled

✅ Pipeline Configuration:
   • Pipeline: Maximum Performance
   • Expected speedup: 4-6x
   • GPU strategy: modern_gpu

Step 7: Configure model hyperparameters
   ⚡ saits: Mixed precision enabled for modern GPU
   ⚡ brits: Mixed precision enabled for modern GPU
✅ Hyperparameters configured with GPU optimizations

Step 8: Data cleaning and quality control

Coverage:
  GR: 89.8%
  NPHI: 73.8%
  RHOB: 63.7%
  RT: 82.4%
  TVDSS: 95.6%
  P-WAVE: 65.3%

Step 8.5: Data sufficiency optimization

📊 Data Sufficiency Analysis:
   • Total wells: 8
   • Median well size: 7832 rows
   • Small wells (< 50 rows): 0.0%
   • Optimal sequence length: 64
✅ Hyperparameters optimized for data sufficiency

Step 9: Running machine learning models...

Model selection options:
• Select multiple models by entering comma-separated numbers (e.g., 1,2,3)
• Type 'all' to select all available models
• Press Enter for default single model selection

📋 Well Log Model Selection Guide:
   Primary Imputation Models (filling missing values in existing sequences):
   1. SAITS - Best for complex geological patterns with long-range dependencies
   2. BRITS - Optimal for maintaining bidirectional geological continuity

   Primary Prediction Models (predicting log values in new wells):
   1. XGBoost - Excellent feature-target learning for cross-well prediction
   2. LightGBM - Fast and accurate for large-scale well log datasets
   3. CatBoost - Robust handling of mixed geological data types
   4. Transformer - Captures complex geological relationships across wells

   Versatile Models (both imputation and prediction):
   1. Transformer - Attention mechanism adapts to both tasks
   2. mRNN - Multi-scale processing handles both local and regional tasks
   3. Gradient Boosting Models - Consistent performance across both scenarios

Select model(s) to run
  1. xgboost
  2. lightgbm
  3. catboost
  4. linear_regression
  5. ridge_regression
  6. lasso_regression
  7. elastic_net
  8. saits
  9. brits
Default: ['xgboost']
Selection: 8
Selected models: saits

Running 1 model(s)...

--- Running Model 1/1: saits ---
🚀 [MAXIMUM PERFORMANCE] Starting optimized training for saits
   Expected speedup: 4-6x
   Optimization level: aggressive
   GPU strategy: modern_gpu
[MEM] Applying memory optimization with Maximum Performance
[MEM] Memory cleared
🎯 GPU Hardware: NVIDIA T550 Laptop GPU (Compute 7.5)
   ⚡ Mixed precision enabled for modern GPU
🚀 Attempting optimized pipeline (level: aggressive)
🚀 Starting Optimized Phase 1 Training (Level: aggressive)...
   Model: SAITS (Self-Attention)
   Target: P-WAVE
   Features: ['GR', 'NPHI', 'RHOB', 'RT', 'TVDSS']
   📊 Dataset size: 57,856 rows, 8 wells (avg: 7232 rows/well)
   🔍 Pre-checking data sufficiency for aggressive optimization...
   📊 Wells analysis: 8/8 wells likely to pass (threshold: 11)
\n📊 Step 1: Optimized Data Preparation...
   Performing quick data quality assessment...
   📊 Small dataset detected (6,000 elements) - using adaptive quality threshold
   📈 Data quality score: 0.657 (finite rate: 66.1%)
   📊 Standard preprocessing required (score: 0.657 < 0.800)
Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=51983/57856 (89.8%)
Normalized 'NPHI': method=standard, valid_data=42700/57856 (73.8%)
Normalized 'RHOB': method=standard, valid_data=36835/57856 (63.7%)
Normalized 'RT': method=standard, valid_data=47696/57856 (82.4%)
Normalized 'TVDSS': method=standard, valid_data=55307/57856 (95.6%)
Normalized 'P-WAVE': method=standard, valid_data=37801/57856 (65.3%)
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: B-G-6:   0%|                            | 0/8 wells [00:00<?, ?wellCreating sequences | Current: B-G-6:  12%|██▌                 | 1/8 wells [00:01<00:07,  1.05s/weCreating sequences | Current: B-G-6:  12%|██▌                 | 1/8 wells [00:01<00:07,  1.05s/weCreating sequences | Current: B-G-10:  12%|██▍                | 1/8 wells [00:01<00:07,  1.05s/weCreating sequences | Current: B-G-10:  25%|████▊              | 2/8 wells [00:02<00:06,  1.06s/weCreating sequences | Current: B-G-10:  25%|████▊              | 2/8 wells [00:02<00:06,  1.06s/weCreating sequences | Current: B-L-1:  25%|█████               | 2/8 wells [00:02<00:06,  1.06s/weCreating sequences | Current: B-L-1:  38%|███████▌            | 3/8 wells [00:02<00:04,  1.04wellCreating sequences | Current: B-L-1:  38%|███████▌            | 3/8 wells [00:02<00:04,  1.04wellCreating sequences | Current: B-L-2.G1:  38%|██████▍          | 3/8 wells [00:02<00:04,  1.04wellCreating sequences | Current: B-L-2.G1:  50%|████████▌        | 4/8 wells [00:04<00:04,  1.06s/weCreating sequences | Current: B-L-2.G1:  50%|████████▌        | 4/8 wells [00:04<00:04,  1.06s/weCreating sequences | Current: B-L-6:  50%|██████████          | 4/8 wells [00:04<00:04,  1.06s/weCreating sequences | Current: B-L-6:  62%|████████████▌       | 5/8 wells [00:04<00:02,  1.11wellCreating sequences | Current: B-L-6:  62%|████████████▌       | 5/8 wells [00:04<00:02,  1.11wellCreating sequences | Current: B-L-9:  62%|████████████▌       | 5/8 wells [00:04<00:02,  1.11wellCreating sequences | Current: B-L-9:  75%|███████████████     | 6/8 wells [00:05<00:01,  1.16wellCreating sequences | Current: B-L-9:  75%|███████████████     | 6/8 wells [00:05<00:01,  1.16wellCreating sequences | Current: B-L-15:  75%|██████████████▎    | 6/8 wells [00:05<00:01,  1.16wellCreating sequences | Current: B-L-15:  88%|████████████████▋  | 7/8 wells [00:06<00:00,  1.16wellCreating sequences | Current: B-L-15:  88%|████████████████▋  | 7/8 wells [00:06<00:00,  1.16wellCreating sequences | Current: EB-1:  88%|██████████████████▍  | 7/8 wells [00:06<00:00,  1.16wellCreating sequences | Current: EB-1: 100%|█████████████████████| 8/8 wells [00:07<00:00,  1.06wellCreating sequences | Current: EB-1: 100%|█████████████████████| 8/8 wells [00:07<00:00,  1.06wellCreating sequences | Current: EB-1: 100%|█████████████████████| 8/8 wells [00:07<00:00,  1.06well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 23
   • Total sequences created: 33,543
Enhanced sequences shape: (33543, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
   Created 33543 sequences in 7.68s
\n🔍 Step 2: Vectorized Preprocessing Pipeline...
   🚀 Vectorized preprocessing: (33543, 64, 6)
   Vectorized preprocessing completed in 0.41s
\n🔧 Step 3: Smart Validation...
   Validation completed in 0.02s
   Sequences valid: ✅ STABLE
\n🎯 Step 4: Direct Tensor Training...
   📚 IMPUTATION MODE: Creating training sequences with missing values
Using enhanced missing value introduction with realistic patterns...
Introduced 26.0% missing values (3354999 elements)
Pattern: 1159245 random + 2195754 chunked
Enhanced missing sequences shape: (33543, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
   Tensor preparation completed in 2.81s
   Training tensor shape: torch.Size([33543, 64, 6])
   Truth tensor shape: torch.Size([33543, 64, 6])
   Efficient synthetic DataFrame created: (100, 8)
--- Running Deep Learning Model: SAITS (Self-Attention) ---
CuDNN benchmark enabled for performance.
Automatic well assignment:
  - Training/Validation wells: ['WELL_8', 'WELL_1', 'WELL_5', 'WELL_0', 'WELL_7', 'WELL_2', 'WELL_9', 'WELL_4']
  - Test wells: ['WELL_3', 'WELL_6']
   Using adaptive validation ratio: 15.0% (median well size: 10)
   🚨 Very small wells detected (min: 10) - reduced validation ratio to 10.0%
   Minimum well size threshold: 6 (validation ratio: 10.0%)
Flexible Split Report:
  - Wells for Training/Validation: 8
  - Wells for Final Testing: 2
  - Train Samples (Shallow part of train wells): 72
  - Validation Samples (Deeper part of train wells): 8
  - Test Samples (Entirely separate wells): 20

Running data leakage detection on splits...
🔍 Performing comprehensive data leakage check...
============================================================
🔍 Detecting perfect correlation leakage...
🚨 POTENTIAL DATA LEAKAGE DETECTED!
   TRAIN split:
     - NPHI: correlation = 0.959
     - RHOB: correlation = 0.994
     - RT: correlation = 1.000
     - TVDSS: correlation = 1.000
🕐 Validating temporal split integrity...
✅ No temporal leakage detected in splits
🎯 Detecting target leakage in features...
🚨 TARGET LEAKAGE DETECTED!
   Suspicious correlations:
     - RHOB: 0.9923
     - RT: 0.9996
     - TVDSS: 0.9999
============================================================
📊 COMPREHENSIVE LEAKAGE CHECK SUMMARY
============================================================
🚨 DATA LEAKAGE DETECTED!
   Failed checks: 2/3
   Data quality score: 0.33

📋 All Recommendations:
   ⚠️ Suspiciously high correlations detected (>0.95)
   • Check if target information is leaking into features
   • Verify temporal ordering of data splits
   • Review feature engineering process
   • Consider removing highly correlated features
   ✅ No temporal leakage detected in splits
   🚨 Target leakage detected in features!
   • Remove features that are identical to target
   • Check feature engineering pipeline for target contamination
   • Verify that future target values are not used as features
   • Review data preprocessing steps
============================================================
⚠️ Data leakage detected but continuing with training...
   Review the recommendations above to improve data quality.

Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=72/72 (100.0%)
Normalized 'NPHI': method=standard, valid_data=72/72 (100.0%)
Normalized 'RHOB': method=standard, valid_data=72/72 (100.0%)
Normalized 'RT': method=standard, valid_data=72/72 (100.0%)
Normalized 'TVDSS': method=standard, valid_data=72/72 (100.0%)
Normalized 'P-WAVE': method=standard, valid_data=72/72 (100.0%)
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:   0%|                           | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:  12%|██▎               | 1/8 wells [00:00<00:00, 944.66wellCreating sequences | Current: WELL_1:  12%|██▎               | 1/8 wells [00:00<00:00, 944.66wellCreating sequences | Current: WELL_1:  25%|████▌             | 2/8 wells [00:00<00:00, 982.85wellCreating sequences | Current: WELL_5:  25%|████▌             | 2/8 wells [00:00<00:00, 982.85wellCreating sequences | Current: WELL_5:  38%|██████▊           | 3/8 wells [00:00<00:00, 938.32wellCreating sequences | Current: WELL_0:  38%|██████▊           | 3/8 wells [00:00<00:00, 938.32wellCreating sequences | Current: WELL_0:  50%|█████████         | 4/8 wells [00:00<00:00, 944.34wellCreating sequences | Current: WELL_7:  50%|█████████         | 4/8 wells [00:00<00:00, 944.34wellCreating sequences | Current: WELL_7:  62%|██████████▋      | 5/8 wells [00:00<00:00, 1180.43wellCreating sequences | Current: WELL_2:  62%|██████████▋      | 5/8 wells [00:00<00:00, 1180.43wellCreating sequences | Current: WELL_2:  75%|████████████▊    | 6/8 wells [00:00<00:00, 1416.52wellCreating sequences | Current: WELL_9:  75%|████████████▊    | 6/8 wells [00:00<00:00, 1416.52wellCreating sequences | Current: WELL_9:  88%|██████████████▉  | 7/8 wells [00:00<00:00, 1652.60wellCreating sequences | Current: WELL_4:  88%|██████████████▉  | 7/8 wells [00:00<00:00, 1652.60wellCreating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1888.69wellCreating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 923.12well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Enhanced sequences shape: (0, 64, 6), type: <class 'numpy.ndarray'>, dtype: float32
⚠️ Enhanced sequence creation failed - falling back to standard method
   Enhanced result shape: (0, 64, 6) (expected: (n_sequences, 64, 6))
   Data quality check:
     - Total wells: 8
     - Total rows: 72
     - Feature columns: ['GR', 'NPHI', 'RHOB', 'RT', 'TVDSS', 'P-WAVE']
     - Sequence length: 64, step: 1
Creating basic sequences:   0%|                                       | 0/8 wells [00:00<?, ?wellCreating basic sequences | Current: WELL_0:   0%|                     | 0/8 wells [00:00<?, ?wellCreating basic sequences | Current: WELL_0:  12%|█▌          | 1/8 wells [00:00<00:00, 539.18wellCreating basic sequences | Current: WELL_1:  12%|█▌          | 1/8 wells [00:00<00:00, 539.18wellCreating basic sequences | Current: WELL_1:  25%|███         | 2/8 wells [00:00<00:00, 518.68wellCreating basic sequences | Current: WELL_2:  25%|███         | 2/8 wells [00:00<00:00, 411.79wellCreating basic sequences | Current: WELL_2:  38%|████▌       | 3/8 wells [00:00<00:00, 512.40wellCreating basic sequences | Current: WELL_4:  38%|████▌       | 3/8 wells [00:00<00:00, 512.40wellCreating basic sequences | Current: WELL_4:  50%|██████      | 4/8 wells [00:00<00:00, 623.25wellCreating basic sequences | Current: WELL_5:  50%|██████      | 4/8 wells [00:00<00:00, 623.25wellCreating basic sequences | Current: WELL_5:  62%|███████▌    | 5/8 wells [00:00<00:00, 544.90wellCreating basic sequences | Current: WELL_7:  62%|███████▌    | 5/8 wells [00:00<00:00, 544.90wellCreating basic sequences | Current: WELL_7:  75%|█████████   | 6/8 wells [00:00<00:00, 589.16wellCreating basic sequences | Current: WELL_8:  75%|█████████   | 6/8 wells [00:00<00:00, 589.16wellCreating basic sequences | Current: WELL_8:  88%|██████████▌ | 7/8 wells [00:00<00:00, 625.99wellCreating basic sequences | Current: WELL_9:  88%|██████████▌ | 7/8 wells [00:00<00:00, 573.56wellCreating basic sequences | Current: WELL_9: 100%|████████████| 8/8 wells [00:00<00:00, 605.63wellCreating basic sequences | Current: WELL_9: 100%|████████████| 8/8 wells [00:00<00:00, 605.63well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Warning: No valid sequences could be created. Check data quality and sequence length.
⚠️ No sequences created with default length. Trying smaller sequence lengths...
   Trying sequence length: 32
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:   0%|                           | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:  12%|███▍                       | 1/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_1:  12%|███▍                       | 1/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_1:  25%|██████▊                    | 2/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_5:  25%|██████▊                    | 2/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_5:  38%|██████████▏                | 3/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_0:  38%|██████████▏                | 3/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_0:  50%|████████▌        | 4/8 wells [00:00<00:00, 1009.88wellCreating sequences | Current: WELL_7:  50%|█████████         | 4/8 wells [00:00<00:00, 804.82wellCreating sequences | Current: WELL_7:  62%|███████████▎      | 5/8 wells [00:00<00:00, 750.70wellCreating sequences | Current: WELL_2:  62%|███████████▎      | 5/8 wells [00:00<00:00, 750.70wellCreating sequences | Current: WELL_2:  75%|█████████████▌    | 6/8 wells [00:00<00:00, 783.49wellCreating sequences | Current: WELL_9:  75%|█████████████▌    | 6/8 wells [00:00<00:00, 783.49wellCreating sequences | Current: WELL_9:  88%|███████████████▊  | 7/8 wells [00:00<00:00, 761.16wellCreating sequences | Current: WELL_4:  88%|███████████████▊  | 7/8 wells [00:00<00:00, 761.16wellCreating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 869.89wellCreating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 777.17well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Enhanced sequences shape: (0, 32, 6), type: <class 'numpy.ndarray'>, dtype: float32
⚠️ Enhanced sequence creation failed - falling back to standard method
   Enhanced result shape: (0, 32, 6) (expected: (n_sequences, 32, 6))
   Data quality check:
     - Total wells: 8
     - Total rows: 72
     - Feature columns: ['GR', 'NPHI', 'RHOB', 'RT', 'TVDSS', 'P-WAVE']
     - Sequence length: 32, step: 1
Creating basic sequences:   0%|                                       | 0/8 wells [00:00<?, ?wellCreating basic sequences | Current: WELL_0:   0%|                     | 0/8 wells [00:00<?, ?wellCreating basic sequences | Current: WELL_0:  12%|█▌          | 1/8 wells [00:00<00:00, 269.97wellCreating basic sequences | Current: WELL_1:  12%|█▌          | 1/8 wells [00:00<00:00, 269.97wellCreating basic sequences | Current: WELL_1:  25%|███         | 2/8 wells [00:00<00:00, 424.63wellCreating basic sequences | Current: WELL_2:  25%|███         | 2/8 wells [00:00<00:00, 348.71wellCreating basic sequences | Current: WELL_2:  38%|████▌       | 3/8 wells [00:00<00:00, 445.51wellCreating basic sequences | Current: WELL_4:  38%|████▌       | 3/8 wells [00:00<00:00, 388.19wellCreating basic sequences | Current: WELL_4:  50%|██████      | 4/8 wells [00:00<00:00, 470.94wellCreating basic sequences | Current: WELL_5:  50%|██████      | 4/8 wells [00:00<00:00, 470.94wellCreating basic sequences | Current: WELL_5:  62%|███████▌    | 5/8 wells [00:00<00:00, 526.37wellCreating basic sequences | Current: WELL_7:  62%|███████▌    | 5/8 wells [00:00<00:00, 484.61wellCreating basic sequences | Current: WELL_7:  75%|█████████   | 6/8 wells [00:00<00:00, 529.76wellCreating basic sequences | Current: WELL_8:  75%|█████████   | 6/8 wells [00:00<00:00, 529.76wellCreating basic sequences | Current: WELL_8:  88%|██████████▌ | 7/8 wells [00:00<00:00, 564.38wellCreating basic sequences | Current: WELL_9:  88%|██████████▌ | 7/8 wells [00:00<00:00, 564.38wellCreating basic sequences | Current: WELL_9: 100%|████████████| 8/8 wells [00:00<00:00, 558.59wellCreating basic sequences | Current: WELL_9: 100%|████████████| 8/8 wells [00:00<00:00, 558.59well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Warning: No valid sequences could be created. Check data quality and sequence length.
   Trying sequence length: 16
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:   0%|                           | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:  12%|██▏              | 1/8 wells [00:00<00:00, 1001.27wellCreating sequences | Current: WELL_1:  12%|██▏              | 1/8 wells [00:00<00:00, 1001.27wellCreating sequences | Current: WELL_1:  25%|████▎            | 2/8 wells [00:00<00:00, 1000.55wellCreating sequences | Current: WELL_5:  25%|████▎            | 2/8 wells [00:00<00:00, 1000.55wellCreating sequences | Current: WELL_5:  38%|██████▍          | 3/8 wells [00:00<00:00, 1018.12wellCreating sequences | Current: WELL_0:  38%|██████▊           | 3/8 wells [00:00<00:00, 867.55wellCreating sequences | Current: WELL_0:  50%|████████▌        | 4/8 wells [00:00<00:00, 1125.38wellCreating sequences | Current: WELL_7:  50%|████████▌        | 4/8 wells [00:00<00:00, 1125.38wellCreating sequences | Current: WELL_7:  62%|██████████▋      | 5/8 wells [00:00<00:00, 1097.47wellCreating sequences | Current: WELL_2:  62%|██████████▋      | 5/8 wells [00:00<00:00, 1097.47wellCreating sequences | Current: WELL_2:  75%|████████████▊    | 6/8 wells [00:00<00:00, 1079.85wellCreating sequences | Current: WELL_9:  75%|████████████▊    | 6/8 wells [00:00<00:00, 1079.85wellCreating sequences | Current: WELL_9:  88%|██████████████▉  | 7/8 wells [00:00<00:00, 1067.60wellCreating sequences | Current: WELL_4:  88%|██████████████▉  | 7/8 wells [00:00<00:00, 1067.60wellCreating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1055.30wellCreating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1055.30well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Enhanced sequences shape: (0, 16, 6), type: <class 'numpy.ndarray'>, dtype: float32
⚠️ Enhanced sequence creation failed - falling back to standard method
   Enhanced result shape: (0, 16, 6) (expected: (n_sequences, 16, 6))
   Data quality check:
     - Total wells: 8
     - Total rows: 72
     - Feature columns: ['GR', 'NPHI', 'RHOB', 'RT', 'TVDSS', 'P-WAVE']
     - Sequence length: 16, step: 1
Creating basic sequences:   0%|                                       | 0/8 wells [00:00<?, ?wellCreating basic sequences | Current: WELL_0:   0%|                     | 0/8 wells [00:00<?, ?wellCreating basic sequences | Current: WELL_0:  12%|█▌          | 1/8 wells [00:00<00:00, 245.09wellCreating basic sequences | Current: WELL_1:  12%|█▌          | 1/8 wells [00:00<00:00, 245.09wellCreating basic sequences | Current: WELL_1:  25%|███         | 2/8 wells [00:00<00:00, 388.56wellCreating basic sequences | Current: WELL_2:  25%|███         | 2/8 wells [00:00<00:00, 323.34wellCreating basic sequences | Current: WELL_2:  38%|████▌       | 3/8 wells [00:00<00:00, 419.32wellCreating basic sequences | Current: WELL_4:  38%|████▌       | 3/8 wells [00:00<00:00, 419.32wellCreating basic sequences | Current: WELL_4:  50%|██████      | 4/8 wells [00:00<00:00, 490.58wellCreating basic sequences | Current: WELL_5:  50%|██████      | 4/8 wells [00:00<00:00, 490.58wellCreating basic sequences | Current: WELL_5:  62%|███████▌    | 5/8 wells [00:00<00:00, 546.25wellCreating basic sequences | Current: WELL_7:  62%|███████▌    | 5/8 wells [00:00<00:00, 491.34wellCreating basic sequences | Current: WELL_7:  75%|█████████   | 6/8 wells [00:00<00:00, 541.53wellCreating basic sequences | Current: WELL_8:  75%|█████████   | 6/8 wells [00:00<00:00, 541.53wellCreating basic sequences | Current: WELL_8:  88%|██████████▌ | 7/8 wells [00:00<00:00, 577.78wellCreating basic sequences | Current: WELL_9:  88%|██████████▌ | 7/8 wells [00:00<00:00, 577.78wellCreating basic sequences | Current: WELL_9: 100%|████████████| 8/8 wells [00:00<00:00, 567.12wellCreating basic sequences | Current: WELL_9: 100%|████████████| 8/8 wells [00:00<00:00, 567.12well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Warning: No valid sequences could be created. Check data quality and sequence length.
   Trying sequence length: 8
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:   0%|                           | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:  12%|███▍                       | 1/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_1:  12%|███▍                       | 1/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_1:  25%|████▌             | 2/8 wells [00:00<00:00, 746.72wellCreating sequences | Current: WELL_5:  25%|████▌             | 2/8 wells [00:00<00:00, 746.72wellCreating sequences | Current: WELL_5:  38%|██████▊           | 3/8 wells [00:00<00:00, 639.08wellCreating sequences | Current: WELL_0:  38%|██████▊           | 3/8 wells [00:00<00:00, 527.70wellCreating sequences | Current: WELL_0:  50%|█████████         | 4/8 wells [00:00<00:00, 557.88wellCreating sequences | Current: WELL_7:  50%|█████████         | 4/8 wells [00:00<00:00, 557.88wellCreating sequences | Current: WELL_7:  62%|███████████▎      | 5/8 wells [00:00<00:00, 576.03wellCreating sequences | Current: WELL_2:  62%|███████████▎      | 5/8 wells [00:00<00:00, 516.79wellCreating sequences | Current: WELL_2:  75%|█████████████▌    | 6/8 wells [00:00<00:00, 538.80wellCreating sequences | Current: WELL_9:  75%|█████████████▌    | 6/8 wells [00:00<00:00, 538.80wellCreating sequences | Current: WELL_9:  88%|███████████████▊  | 7/8 wells [00:00<00:00, 576.79wellCreating sequences | Current: WELL_4:  88%|███████████████▊  | 7/8 wells [00:00<00:00, 576.79wellCreating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 659.18wellCreating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 659.18well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 16
Enhanced sequences shape: (16, 8, 6), type: <class 'numpy.ndarray'>, dtype: float64
Successfully created 16 sequences with length 8
Using enhanced normalization with winsorization...
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:   0%|                           | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:  12%|██▎               | 1/8 wells [00:00<00:00, 500.39wellCreating sequences | Current: WELL_1:  12%|██▎               | 1/8 wells [00:00<00:00, 500.39wellCreating sequences | Current: WELL_1:  25%|████▌             | 2/8 wells [00:00<00:00, 500.84wellCreating sequences | Current: WELL_5:  25%|████▌             | 2/8 wells [00:00<00:00, 500.84wellCreating sequences | Current: WELL_5:  38%|██████▊           | 3/8 wells [00:00<00:00, 601.08wellCreating sequences | Current: WELL_0:  38%|██████▊           | 3/8 wells [00:00<00:00, 601.08wellCreating sequences | Current: WELL_0:  50%|█████████         | 4/8 wells [00:00<00:00, 667.67wellCreating sequences | Current: WELL_7:  50%|█████████         | 4/8 wells [00:00<00:00, 667.67wellCreating sequences | Current: WELL_7:  62%|███████████▎      | 5/8 wells [00:00<00:00, 715.36wellCreating sequences | Current: WELL_2:  62%|███████████▎      | 5/8 wells [00:00<00:00, 715.36wellCreating sequences | Current: WELL_2:  75%|█████████████▌    | 6/8 wells [00:00<00:00, 750.84wellCreating sequences | Current: WELL_9:  75%|█████████████▌    | 6/8 wells [00:00<00:00, 750.84wellCreating sequences | Current: WELL_9:  88%|███████████████▊  | 7/8 wells [00:00<00:00, 875.98wellCreating sequences | Current: WELL_4:  88%|███████████████▊  | 7/8 wells [00:00<00:00, 778.68wellCreating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 889.92wellCreating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 889.92well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Enhanced sequences shape: (0, 8, 6), type: <class 'numpy.ndarray'>, dtype: float32
⚠️ Enhanced sequence creation failed - falling back to standard method
   Enhanced result shape: (0, 8, 6) (expected: (n_sequences, 8, 6))
   Data quality check:
     - Total wells: 8
     - Total rows: 8
     - Feature columns: ['GR', 'NPHI', 'RHOB', 'RT', 'TVDSS', 'P-WAVE']
     - Sequence length: 8, step: 1
Creating basic sequences:   0%|                                       | 0/8 wells [00:00<?, ?wellCreating basic sequences | Current: WELL_0:   0%|                     | 0/8 wells [00:00<?, ?wellCreating basic sequences | Current: WELL_0:  12%|█▍         | 1/8 wells [00:00<00:00, 1001.03wellCreating basic sequences | Current: WELL_1:  12%|█▍         | 1/8 wells [00:00<00:00, 1001.03wellCreating basic sequences | Current: WELL_1:  25%|███         | 2/8 wells [00:00<00:00, 787.74wellCreating basic sequences | Current: WELL_2:  25%|███         | 2/8 wells [00:00<00:00, 787.74wellCreating basic sequences | Current: WELL_2:  38%|████▌       | 3/8 wells [00:00<00:00, 846.14wellCreating basic sequences | Current: WELL_4:  38%|████▌       | 3/8 wells [00:00<00:00, 658.86wellCreating basic sequences | Current: WELL_4:  50%|██████      | 4/8 wells [00:00<00:00, 721.14wellCreating basic sequences | Current: WELL_5:  50%|██████      | 4/8 wells [00:00<00:00, 721.14wellCreating basic sequences | Current: WELL_5:  62%|███████▌    | 5/8 wells [00:00<00:00, 662.48wellCreating basic sequences | Current: WELL_7:  62%|███████▌    | 5/8 wells [00:00<00:00, 662.48wellCreating basic sequences | Current: WELL_7:  75%|█████████   | 6/8 wells [00:00<00:00, 628.52wellCreating basic sequences | Current: WELL_8:  75%|█████████   | 6/8 wells [00:00<00:00, 628.52wellCreating basic sequences | Current: WELL_8:  88%|██████████▌ | 7/8 wells [00:00<00:00, 663.66wellCreating basic sequences | Current: WELL_9:  88%|██████████▌ | 7/8 wells [00:00<00:00, 663.66wellCreating basic sequences | Current: WELL_9: 100%|████████████| 8/8 wells [00:00<00:00, 690.79wellCreating basic sequences | Current: WELL_9: 100%|████████████| 8/8 wells [00:00<00:00, 690.79well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Warning: No valid sequences could be created. Check data quality and sequence length.
📚 Introducing missingness for imputation training.
Using enhanced missing value introduction with realistic patterns...
Introduced 15.5% missing values (119 elements)
Pattern: 69 random + 50 chunked
Enhanced missing sequences shape: (16, 8, 6), type: <class 'numpy.ndarray'>, dtype: float64      
Using enhanced missing value introduction with realistic patterns...
❌ Optimized training failed: not enough values to unpack (expected 3, got 1)
   Falling back to original function...
--- Running Deep Learning Model: SAITS (Self-Attention) ---
CuDNN benchmark enabled for performance.
Automatic well assignment:
  - Training/Validation wells: ['B-G-10', 'B-L-9', 'B-G-6', 'EB-1', 'B-L-1', 'B-L-6']
  - Test wells: ['B-L-2.G1', 'B-L-15']
   Using adaptive validation ratio: 30.0% (median well size: 7979)
   Minimum well size threshold: 12 (validation ratio: 30.0%)
Flexible Split Report:
  - Wells for Training/Validation: 6
  - Wells for Final Testing: 2
  - Train Samples (Shallow part of train wells): 32126
  - Validation Samples (Deeper part of train wells): 13772
  - Test Samples (Entirely separate wells): 11958

Running data leakage detection on splits...
🔍 Performing comprehensive data leakage check...
============================================================
🔍 Detecting perfect correlation leakage...
🚨 POTENTIAL DATA LEAKAGE DETECTED!
   TEST split:
     - TVDSS: correlation = 0.966
🕐 Validating temporal split integrity...
✅ No temporal leakage detected in splits
🎯 Detecting target leakage in features...
✅ No target leakage detected in features
============================================================
📊 COMPREHENSIVE LEAKAGE CHECK SUMMARY
============================================================
🚨 DATA LEAKAGE DETECTED!
   Failed checks: 1/3
   Data quality score: 0.67

📋 All Recommendations:
   ⚠️ Suspiciously high correlations detected (>0.95)
   • Check if target information is leaking into features
   • Verify temporal ordering of data splits
   • Review feature engineering process
   • Consider removing highly correlated features
   ✅ No temporal leakage detected in splits
   ✅ No target leakage detected in features
============================================================
⚠️ Data leakage detected but continuing with training...
   Review the recommendations above to improve data quality.

Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=31964/32126 (99.5%)
Normalized 'NPHI': method=standard, valid_data=24749/32126 (77.0%)
Normalized 'RHOB': method=standard, valid_data=20743/32126 (64.6%)
Normalized 'RT': method=standard, valid_data=31442/32126 (97.9%)
Normalized 'TVDSS': method=standard, valid_data=32126/32126 (100.0%)
Normalized 'P-WAVE': method=standard, valid_data=21526/32126 (67.0%)
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/6 wells [00:00<?, ?wellCreating sequences | Current: B-G-10:   0%|                           | 0/6 wells [00:00<?, ?wellCreating sequences | Current: B-G-10:  17%|███▏               | 1/6 wells [00:00<00:04,  1.09wellCreating sequences | Current: B-G-10:  17%|███▏               | 1/6 wells [00:00<00:04,  1.09wellCreating sequences | Current: B-L-9:  17%|███▎                | 1/6 wells [00:00<00:04,  1.09wellCreating sequences | Current: B-L-9:  33%|██████▋             | 2/6 wells [00:01<00:02,  1.53wellCreating sequences | Current: B-L-9:  33%|██████▋             | 2/6 wells [00:01<00:02,  1.53wellCreating sequences | Current: B-G-6:  33%|██████▋             | 2/6 wells [00:01<00:02,  1.53wellCreating sequences | Current: B-G-6:  50%|██████████          | 3/6 wells [00:02<00:02,  1.23wellCreating sequences | Current: B-G-6:  50%|██████████          | 3/6 wells [00:02<00:02,  1.23wellCreating sequences | Current: EB-1:  50%|██████████▌          | 3/6 wells [00:02<00:02,  1.23wellCreating sequences | Current: EB-1:  67%|██████████████       | 4/6 wells [00:03<00:01,  1.23wellCreating sequences | Current: EB-1:  67%|██████████████       | 4/6 wells [00:03<00:01,  1.23wellCreating sequences | Current: B-L-1:  67%|█████████████▎      | 4/6 wells [00:03<00:01,  1.23wellCreating sequences | Current: B-L-1:  83%|████████████████▋   | 5/6 wells [00:03<00:00,  1.33wellCreating sequences | Current: B-L-1:  83%|████████████████▋   | 5/6 wells [00:03<00:00,  1.33wellCreating sequences | Current: B-L-6:  83%|████████████████▋   | 5/6 wells [00:03<00:00,  1.33wellCreating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:04<00:00,  1.59wellCreating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:04<00:00,  1.59wellCreating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:04<00:00,  1.42well/s]

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 15
   • Total sequences created: 18,884
Enhanced sequences shape: (18884, 8, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced normalization with winsorization...
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/6 wells [00:00<?, ?wellCreating sequences | Current: B-G-10:   0%|                           | 0/6 wells [00:00<?, ?wellCreating sequences | Current: B-G-10:  17%|███▏               | 1/6 wells [00:00<00:00,  6.05wellCreating sequences | Current: B-G-10:  17%|███▏               | 1/6 wells [00:00<00:00,  6.05wellCreating sequences | Current: B-L-9:  17%|███▎                | 1/6 wells [00:00<00:00,  6.05wellCreating sequences | Current: B-L-9:  33%|██████▋             | 2/6 wells [00:00<00:01,  3.84wellCreating sequences | Current: B-L-9:  33%|██████▋             | 2/6 wells [00:00<00:01,  3.84wellCreating sequences | Current: B-G-6:  33%|██████▋             | 2/6 wells [00:00<00:01,  3.84wellCreating sequences | Current: B-G-6:  50%|██████████          | 3/6 wells [00:00<00:00,  4.97wellCreating sequences | Current: B-G-6:  50%|██████████          | 3/6 wells [00:00<00:00,  4.97wellCreating sequences | Current: EB-1:  50%|██████████▌          | 3/6 wells [00:00<00:00,  4.97wellCreating sequences | Current: EB-1:  67%|██████████████       | 4/6 wells [00:00<00:00,  4.08wellCreating sequences | Current: EB-1:  67%|██████████████       | 4/6 wells [00:00<00:00,  4.08wellCreating sequences | Current: B-L-1:  67%|█████████████▎      | 4/6 wells [00:00<00:00,  4.08wellCreating sequences | Current: B-L-1:  83%|████████████████▋   | 5/6 wells [00:01<00:00,  4.16wellCreating sequences | Current: B-L-1:  83%|████████████████▋   | 5/6 wells [00:01<00:00,  4.16wellCreating sequences | Current: B-L-6:  83%|████████████████▋   | 5/6 wells [00:01<00:00,  4.16wellCreating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:01<00:00,  4.17wellCreating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:01<00:00,  4.17wellCreating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:01<00:00,  4.27well/s]

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 6
   • Total sequences created: 6,267
Enhanced sequences shape: (6267, 8, 6), type: <class 'numpy.ndarray'>, dtype: float64
📚 Introducing missingness for imputation training.
Using enhanced missing value introduction with realistic patterns...
Introduced 15.8% missing values (143340 elements)
Pattern: 81578 random + 61762 chunked
Enhanced missing sequences shape: (18884, 8, 6), type: <class 'numpy.ndarray'>, dtype: float64   
Using enhanced missing value introduction with realistic patterns...
Introduced 15.8% missing values (47568 elements)
Pattern: 27073 random + 20495 chunked
Enhanced missing sequences shape: (6267, 8, 6), type: <class 'numpy.ndarray'>, dtype: float64    
Converting sequences to tensors...
   Processing train_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing train_sequences_missing dtype from float64 to float32.
   train_sequences_missing tensor created with shape: torch.Size([18884, 8, 6]), dtype: torch.float32
   Processing train_sequences_true: current type is <class 'numpy.ndarray'>
   Changing train_sequences_true dtype from float64 to float32.
   train_sequences_true tensor created with shape: torch.Size([18884, 8, 6]), dtype: torch.float32
   Processing val_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing val_sequences_missing dtype from float64 to float32.
   val_sequences_missing tensor created with shape: torch.Size([6267, 8, 6]), dtype: torch.float32
   Processing val_sequences_true: current type is <class 'numpy.ndarray'>
   Changing val_sequences_true dtype from float64 to float32.
   val_sequences_true tensor created with shape: torch.Size([6267, 8, 6]), dtype: torch.float32  
   Tensor shapes - Train: torch.Size([18884, 8, 6]), Truth: torch.Size([18884, 8, 6])
🔧 Applied fixed parameters for SAITS (Self-Attention): {}
Initialized SAITSModel with:
   - Features: 6
   - Sequence length: 8
   - Epochs: 40
   - Batch size: 64
   - Learning rate: 0.002
❌ Failed to create SAITS (Self-Attention) model: PyPOTS is required for SAITS model. Please install with: pip install pypots
   Parameters: {'sequence_len': 8, 'n_features': 6, 'n_layers': 2, 'd_model': 256, 'n_heads': 4, 'epochs': 40, 'batch_size': 64, 'learning_rate': 0.002, 'early_stopping_patience': 12, 'dropout': 0.1, 'device': None, 'use_mixed_precision': True, 'gpu_config': {'mixed_precision_enabled': True, 'gpu_preprocessing_enabled': True, 'compute_capability': '7.5', 'optimization_strategy': 'modern_gpu'}}
⚠️ Optimization failed after 17.16s: PyPOTS is required for SAITS model. Please install with: pipp install pypots
   🔄 Falling back to original implementation...
🚀 Starting Phase 1 Enhanced Deep Learning Training...
   Model: SAITS (Self-Attention)
   Target: P-WAVE
   Features: ['GR', 'NPHI', 'RHOB', 'RT', 'TVDSS']

📊 Step 1: Initial Data Preparation...
Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=51983/57856 (89.8%)
Normalized 'NPHI': method=standard, valid_data=42700/57856 (73.8%)
Normalized 'RHOB': method=standard, valid_data=36835/57856 (63.7%)
Normalized 'RT': method=standard, valid_data=47696/57856 (82.4%)
Normalized 'TVDSS': method=standard, valid_data=55307/57856 (95.6%)
Normalized 'P-WAVE': method=standard, valid_data=37801/57856 (65.3%)
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: B-G-6:   0%|                            | 0/8 wells [00:00<?, ?wellCreating sequences | Current: B-G-6:  12%|██▌                 | 1/8 wells [00:01<00:07,  1.06s/weCreating sequences | Current: B-G-6:  12%|██▌                 | 1/8 wells [00:01<00:07,  1.06s/weCreating sequences | Current: B-G-10:  12%|██▍                | 1/8 wells [00:01<00:07,  1.06s/weCreating sequences | Current: B-G-10:  25%|████▊              | 2/8 wells [00:02<00:06,  1.06s/weCreating sequences | Current: B-G-10:  25%|████▊              | 2/8 wells [00:02<00:06,  1.06s/weCreating sequences | Current: B-L-1:  25%|█████               | 2/8 wells [00:02<00:06,  1.06s/weCreating sequences | Current: B-L-1:  38%|███████▌            | 3/8 wells [00:02<00:04,  1.04wellCreating sequences | Current: B-L-1:  38%|███████▌            | 3/8 wells [00:02<00:04,  1.04wellCreating sequences | Current: B-L-2.G1:  38%|██████▍          | 3/8 wells [00:02<00:04,  1.04wellCreating sequences | Current: B-L-2.G1:  50%|████████▌        | 4/8 wells [00:04<00:04,  1.06s/weCreating sequences | Current: B-L-2.G1:  50%|████████▌        | 4/8 wells [00:04<00:04,  1.06s/weCreating sequences | Current: B-L-6:  50%|██████████          | 4/8 wells [00:04<00:04,  1.06s/weCreating sequences | Current: B-L-6:  62%|████████████▌       | 5/8 wells [00:04<00:02,  1.12wellCreating sequences | Current: B-L-6:  62%|████████████▌       | 5/8 wells [00:04<00:02,  1.12wellCreating sequences | Current: B-L-9:  62%|████████████▌       | 5/8 wells [00:04<00:02,  1.12wellCreating sequences | Current: B-L-9:  75%|███████████████     | 6/8 wells [00:05<00:01,  1.16wellCreating sequences | Current: B-L-9:  75%|███████████████     | 6/8 wells [00:05<00:01,  1.16wellCreating sequences | Current: B-L-15:  75%|██████████████▎    | 6/8 wells [00:05<00:01,  1.16wellCreating sequences | Current: B-L-15:  88%|████████████████▋  | 7/8 wells [00:06<00:00,  1.16wellCreating sequences | Current: B-L-15:  88%|████████████████▋  | 7/8 wells [00:06<00:00,  1.16wellCreating sequences | Current: EB-1:  88%|██████████████████▍  | 7/8 wells [00:06<00:00,  1.16wellCreating sequences | Current: EB-1: 100%|█████████████████████| 8/8 wells [00:07<00:00,  1.01wellCreating sequences | Current: EB-1: 100%|█████████████████████| 8/8 wells [00:07<00:00,  1.01wellCreating sequences | Current: EB-1: 100%|█████████████████████| 8/8 wells [00:07<00:00,  1.04well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 23
   • Total sequences created: 33,543
Enhanced sequences shape: (33543, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
   Created 33543 sequences
   Sequence shape: (33543, 64, 6)

🔍 Step 2: Phase 1 Advanced Preprocessing...
   Dataset characteristics:
     • Sequences: 33543
     • Missing rate: 0.0%
     • Recommended config: {'normalization_method': 'quantile', 'missing_encoding_method': 'learnable_embedding', 'validate_ranges': True}
INFO:preprocessing.deep_model.stability_preprocessing:🚀 Starting Phase 1 Advanced Preprocessing Pipeline...
INFO:preprocessing.deep_model.stability_preprocessing:
🔍 Step 1: Advanced Input Validation & Cleaning...
INFO:preprocessing.deep_model.stability_preprocessing:
❓ Step 2: Missing Value Encoding (learnable_embedding)...
INFO:preprocessing.deep_model.stability_preprocessing:
📊 Step 3: Robust Normalization (quantile)...
INFO:preprocessing.deep_model.stability_preprocessing:
🔧 Step 4: Final Numerical Stability Check...
INFO:preprocessing.deep_model.stability_preprocessing:✅ Final sequences pass all stability checks
INFO:preprocessing.deep_model.stability_preprocessing:
🎉 Phase 1 preprocessing pipeline completed successfully!
INFO:preprocessing.deep_model.stability_preprocessing:   Input shape: (33543, 64, 6) → Output shape: (33543, 64, 6)
INFO:preprocessing.deep_model.stability_preprocessing:   Data quality score: 0.950
INFO:preprocessing.deep_model.stability_preprocessing:   Ready for stable deep learning training!
✅ Phase 1 preprocessing completed:
   • Data quality score: 0.950
❌ Fallback also failed: 'missing_rate_before'
[MEM] Memory cleared
saits failed with error: Both optimized and fallback implementations failed. Optimization error: PyPOTS is required for SAITS model. Please install with: pip install pypots, Fallback error: 'missing_rate_before'

Batch execution summary:
   • Successful models: 0
   • Failed models: 1
   • Failed: saits
All models failed. Continuing to next step...

Step 10: Configure output options
No successful models to process. Skipping to next step...