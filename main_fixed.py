#!/usr/bin/env python3
"""
Fixed Main Script for ML Log Prediction

This script pre-loads TensorFlow and PyPOTS correctly before starting the main application
to avoid the cached error states that cause startup warnings.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def preload_dependencies():
    """Pre-load TensorFlow and PyPOTS to avoid cached error states."""
    print("🔄 Pre-loading dependencies...")
    
    try:
        # Force TensorFlow to load correctly first
        import tensorflow as tf
        print(f"✅ TensorFlow {tf.__version__} pre-loaded successfully")
        
        # Force PyPOTS to load correctly
        import pypots
        print(f"✅ PyPOTS {pypots.__version__} pre-loaded successfully")
        
        # Reset the compatibility module cache
        from utils.tensorflow_compatibility import reset_tensorflow_cache
        reset_tensorflow_cache()
        print("✅ TensorFlow compatibility cache reset")
        
        return True
        
    except Exception as e:
        print(f"⚠️ Pre-loading failed: {e}")
        print("   Continuing with main application anyway...")
        return False

def main():
    """Main entry point with dependency pre-loading."""
    print("🚀 Starting ML Log Prediction with Fixed Dependencies")
    print("=" * 60)
    
    # Pre-load dependencies
    preload_success = preload_dependencies()
    
    if preload_success:
        print("✅ Dependencies pre-loaded successfully")
    else:
        print("⚠️ Some dependencies failed to pre-load")
    
    print("=" * 60)
    print("🔄 Starting main application...")
    print()
    
    # Import and run the main application
    try:
        # Import main after pre-loading dependencies
        import main as main_module
        
        # The main module will run automatically when imported
        # since it has the main execution logic at module level
        
    except Exception as e:
        print(f"❌ Main application failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
