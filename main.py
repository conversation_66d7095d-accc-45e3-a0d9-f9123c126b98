#!/usr/bin/env python3
"""
ML Log Prediction with GUI file selection dialog for LAS files.
Enhanced with transformer memory optimization (Phase 1).
"""

import os
import sys

# Phase 1 Step 0: Environment Setup and Diagnostics
print("[PHASE1] Setting up development environment...")
try:
    from utils.environment_setup import initialize_environment_once
    diagnostics = initialize_environment_once()
    if diagnostics is None:
        print("   [INFO] Environment already initialized")
except ImportError:
    print("   [WARN] Environment setup module not available - using basic configuration")
    # Basic environment setup
    import warnings
    warnings.filterwarnings('ignore', category=UserWarning, message='.*Failed to find MSVC.*')
    warnings.filterwarnings('ignore', category=UserWarning, message='.*Failed to find Windows SDK.*')
    warnings.filterwarnings('ignore', category=UserWarning, message='.*Failed to find CUDA.*')

# Phase 1 Step 1: Memory Optimization Configuration - Set before any PyTorch imports
print("[PHASE1] Configuring memory optimization environment...")
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
print("   [OK] PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True")

from core_code.data_handler import load_las_files_from_directory, clean_log_data, write_results_to_las
from config_handler import (get_input_files, select_output_directory, configure_log_selection,
                          configure_well_separation, get_prediction_mode, configure_hyperparameters,
                          console_select, configure_deep_learning_pipeline, configure_gpu_optimization,
                          apply_gpu_optimizations_to_hparams, apply_data_sufficiency_optimizations)
from core_code.ml_core import impute_logs, impute_logs_deep, MODEL_REGISTRY

# Phase 1 Step 3: Import Advanced Data Preprocessing Pipeline
try:
    # Try importing from the main integration module first (has optimization_level support)
    from ml_core_phase1_integration import (impute_logs_deep_phase1, impute_logs_deep_phase1_safe,
                                           impute_logs_deep_phase1_optimized)
    PHASE1_PREPROCESSING_AVAILABLE = True
    print("   [OK] Phase 1 Enhanced Deep Learning Integration loaded (ml_core_phase1_integration)")
    print("   [OK] Optimized pipeline functions available")
except ImportError as e1:
    try:
        # Fallback to preprocessing module (without optimization_level support)
        from preprocessing.deep_model.phase1_integration import (impute_logs_deep_phase1, impute_logs_deep_phase1_safe,
                                               impute_logs_deep_phase1_optimized)
        PHASE1_PREPROCESSING_AVAILABLE = True
        print("   [OK] Phase 1 Enhanced Deep Learning Integration loaded (preprocessing fallback)")
        print("   [WARN] optimization_level parameter not supported in this version")
    except ImportError as e2:
        print(f"   [WARN] Phase 1 enhanced integration not available: {e1}, {e2}")
        PHASE1_PREPROCESSING_AVAILABLE = False
        impute_logs_deep_phase1 = None
        impute_logs_deep_phase1_safe = None
        impute_logs_deep_phase1_optimized = None
from reporting import (generate_qc_report, create_summary_plots, generate_final_report,
                      create_multi_model_comparison_plots, create_separate_comparison_plots,
                      create_crossplot_analysis, create_model_ranking_visualization)

# Phase 1 Step 2: Initialize Memory Optimizer
try:
    from utils.memory_optimization import get_memory_optimizer
    memory_optimizer = get_memory_optimizer(
        enable_mixed_precision=True,
        enable_monitoring=True
    )
    print("   [OK] Memory optimizer initialized")
    MEMORY_OPTIMIZATION_AVAILABLE = True
except ImportError as e:
    print(f"   [WARN] Memory optimization not available: {e}")
    memory_optimizer = None
    MEMORY_OPTIMIZATION_AVAILABLE = False

def main():
    """Run the ML log prediction workflow with GUI file selection."""
    print("=" * 60)
    print(" ML LOG PREDICTION")
    print("=" * 60)
    
    # Phase 1 Step 2: Display initial memory status
    if MEMORY_OPTIMIZATION_AVAILABLE and memory_optimizer:
        print("\n[MEM] Initial Memory Status:")
        memory_optimizer.print_memory_status()
    
    # Step 1: Get input files using file dialog
    print("\nStep 1: Select LAS files")
    inp = get_input_files()
    
    if not inp:
        print("File selection cancelled. Exiting.")
        return
    
    # Step 2: Load LAS files
    print("\nStep 2: Loading LAS files...")
    df, las_objs, wells, logs = load_las_files_from_directory(inp)
    
    if df.empty:
        print("No data loaded. Exiting.")
        return
    
    print(f"Successfully loaded:")
    print(f"   • {len(df)} data points")
    print(f"   • {len(wells)} wells: {', '.join(wells)}")
    print(f"   • {len(logs)} log curves: {', '.join(logs)}")
    
    # Step 3: Configure log selection
    print("\nStep 3: Configure feature and target logs")
    feats, tgt = configure_log_selection(logs)
    print(f"Feature logs: {', '.join(feats)}")
    print(f"Target log: {tgt}")
    
    # Step 4: Configure well separation
    print("\nStep 4: Configure training/prediction strategy")
    cfg = configure_well_separation(wells)
    print(f"Mode: {cfg['mode']}")
    
    # Step 5: Configure prediction mode
    print("\nStep 5: Configure prediction mode")
    mode = get_prediction_mode()
    print(f"Prediction mode: {mode}")
    
    # Step 6: Configure deep learning pipeline optimization
    print("\nStep 6: Configure deep learning pipeline optimization")
    pipeline_config = configure_deep_learning_pipeline()
    gpu_config = configure_gpu_optimization()

    print(f"\n✅ Pipeline Configuration:")
    print(f"   • Pipeline: {pipeline_config['pipeline_name']}")
    print(f"   • Expected speedup: {pipeline_config['expected_speedup']}")
    print(f"   • GPU strategy: {gpu_config['optimization_strategy']}")

    # Step 7: Configure hyperparameters with optimizations
    print("\nStep 7: Configure model hyperparameters")
    hparams = configure_hyperparameters()
    hparams = apply_gpu_optimizations_to_hparams(hparams, gpu_config)
    print("✅ Hyperparameters configured with GPU optimizations")
    
    # Step 8: Data cleaning and QC
    print("\nStep 8: Data cleaning and quality control")
    clean_df = clean_log_data(df)
    generate_qc_report(clean_df, feats+[tgt], cfg)

    # Step 8.5: Apply data sufficiency optimizations for deep learning models
    print("\nStep 8.5: Data sufficiency optimization")
    hparams = apply_data_sufficiency_optimizations(hparams, clean_df, feats, tgt)
    print("✅ Hyperparameters optimized for data sufficiency")

    # Main processing loop - allows running multiple models
    while True:
        # Step 9: Machine learning prediction
        print("\nStep 9: Running machine learning models...")

        # Multi-model selection interface
        available_models = list(MODEL_REGISTRY.keys())
        print("\nModel selection options:")
        print("• Select multiple models by entering comma-separated numbers (e.g., 1,2,3)")
        print("• Type 'all' to select all available models")
        print("• Press Enter for default single model selection")
        
        # Display well log specific model context information
        print("\n📋 Well Log Model Selection Guide:")
        print("   Primary Imputation Models (filling missing values in existing sequences):")
        print("   1. SAITS - Best for complex geological patterns with long-range dependencies")
        print("   2. BRITS - Optimal for maintaining bidirectional geological continuity")
        print("")
        print("   Primary Prediction Models (predicting log values in new wells):")
        print("   1. XGBoost - Excellent feature-target learning for cross-well prediction")
        print("   2. LightGBM - Fast and accurate for large-scale well log datasets")
        print("   3. CatBoost - Robust handling of mixed geological data types")
        print("   4. Transformer - Captures complex geological relationships across wells")
        print("")
        print("   Versatile Models (both imputation and prediction):")
        print("   1. Transformer - Attention mechanism adapts to both tasks")
        print("   2. mRNN - Multi-scale processing handles both local and regional tasks")
        print("   3. Gradient Boosting Models - Consistent performance across both scenarios")

        selected_model_keys = console_select(
            available_models,
            "Select model(s) to run",
            default=['xgboost'],
            multiple=True
        )

        # Ensure we have a list even for single selection
        if not isinstance(selected_model_keys, list):
            selected_model_keys = [selected_model_keys]

        # Flatten any nested lists and ensure all items are strings
        flattened_models = []
        for item in selected_model_keys:
            if isinstance(item, list):
                flattened_models.extend(item)
            else:
                flattened_models.append(item)
        selected_model_keys = flattened_models

        print(f"Selected models: {', '.join(selected_model_keys)}")

        # Batch model execution
        all_results = {}
        combined_evaluations = []
        successful_models = []
        failed_models = []

        print(f"\nRunning {len(selected_model_keys)} model(s)...")

        for i, model_key in enumerate(selected_model_keys, 1):
            print(f"\n--- Running Model {i}/{len(selected_model_keys)}: {model_key} ---")
            selected_model_config = MODEL_REGISTRY[model_key]

            try:
                # Check the model type to decide which function to call
                if selected_model_config.get('type') in ['deep', 'deep_advanced']:
                    # Run the deep learning workflow with optimized pipeline selection
                    hparams_selected = hparams[model_key]

                    # Apply pipeline configuration based on user selection
                    if pipeline_config['use_optimized_pipeline'] and PHASE1_PREPROCESSING_AVAILABLE:
                        optimization_level = pipeline_config['optimization_level']
                        pipeline_name = pipeline_config['pipeline_name']
                        expected_speedup = pipeline_config['expected_speedup']

                        print(f"🚀 [{pipeline_name.upper()}] Starting optimized training for {model_key}")
                        print(f"   Expected speedup: {expected_speedup}")
                        print(f"   Optimization level: {optimization_level}")
                        print(f"   GPU strategy: {gpu_config['optimization_strategy']}")

                        # Check if the function supports optimization_level parameter
                        import inspect
                        sig = inspect.signature(impute_logs_deep_phase1_safe)
                        supports_optimization_level = 'optimization_level' in sig.parameters

                        if MEMORY_OPTIMIZATION_AVAILABLE and memory_optimizer:
                            print(f"[MEM] Applying memory optimization with {pipeline_name}")
                            with memory_optimizer.memory_efficient_context():
                                if supports_optimization_level:
                                    res_df, mres = impute_logs_deep_phase1_safe(
                                        clean_df, feats, tgt, selected_model_config, hparams_selected,
                                        optimization_level=optimization_level
                                    )
                                else:
                                    print(f"   [WARN] optimization_level not supported, using default parameters")
                                    res_df, mres = impute_logs_deep_phase1_safe(
                                        clean_df, tgt, feats, model_config=selected_model_config, hparams=hparams_selected
                                    )
                            print(f"✅ [{pipeline_name.upper()}] Completed optimized training for {model_key} with memory optimization")
                        else:
                            if supports_optimization_level:
                                res_df, mres = impute_logs_deep_phase1_safe(
                                    clean_df, feats, tgt, selected_model_config, hparams_selected,
                                    optimization_level=optimization_level
                                )
                            else:
                                print(f"   [WARN] optimization_level not supported, using default parameters")
                                res_df, mres = impute_logs_deep_phase1_safe(
                                    clean_df, tgt, feats, model_config=selected_model_config, hparams=hparams_selected
                                )
                            print(f"✅ [{pipeline_name.upper()}] Completed optimized training for {model_key}")
                    else:
                        # Use original pipeline
                        print(f"📚 [ORIGINAL] Starting standard training for {model_key}")
                        print(f"   Using original ml_core.py pipeline")

                        if MEMORY_OPTIMIZATION_AVAILABLE and memory_optimizer:
                            print(f"[MEM] Starting memory-optimized training for {model_key}")
                            with memory_optimizer.memory_efficient_context():
                                res_df, mres = impute_logs_deep(clean_df, feats, tgt, selected_model_config, hparams_selected)
                            print(f"✅ [ORIGINAL] Completed training for {model_key} with memory optimization")
                        else:
                            res_df, mres = impute_logs_deep(clean_df, feats, tgt, selected_model_config, hparams_selected)
                            print(f"✅ [ORIGINAL] Completed training for {model_key}")
                else:
                    # Run the existing shallow model workflow
                    models_to_run = {selected_model_config['name']: selected_model_config['model_class'](**hparams[model_key])}
                    res_df, mres = impute_logs(clean_df, feats, tgt, models_to_run, cfg, mode)

                if mres and res_df is not None:
                    # Store results for this model
                    all_results[model_key] = {
                        'res_df': res_df,
                        'mres': mres,
                        'model_config': selected_model_config
                    }

                    # Add evaluations to combined list with model identifier
                    for eval_result in mres['evaluations']:
                        eval_with_key = eval_result.copy()
                        eval_with_key['model_key'] = model_key
                        combined_evaluations.append(eval_with_key)

                    successful_models.append(model_key)
                    print(f"{model_key} completed successfully")
                else:
                    failed_models.append(model_key)
                    print(f"{model_key} failed to produce results")

            except Exception as e:
                failed_models.append(model_key)
                print(f"{model_key} failed with error: {str(e)}")

        # Results summary
        print(f"\nBatch execution summary:")
        print(f"   • Successful models: {len(successful_models)}")
        print(f"   • Failed models: {len(failed_models)}")

        if successful_models:
            print(f"   • Successful: {', '.join(successful_models)}")
        if failed_models:
            print(f"   • Failed: {', '.join(failed_models)}")

        if not successful_models:
            print("All models failed. Continuing to next step...")
            res_df, mres = None, None
        else:
            print("Multi-model execution completed")

        # Step 10: Configure output options
        print("\nStep 10: Configure output options")

        if not successful_models:
            print("No successful models to process. Skipping to next step...")
        else:
            # Always display model performance comparison for successful models
            print("\nModel Performance Summary:")
            print("-" * 70)
            print(f"{'Model':<15} {'MAE':<10} {'R²':<10} {'RMSE':<10} {'Composite':<12}")
            print("-" * 70)

            # Sort evaluations by composite score for ranking
            sorted_evals = sorted(combined_evaluations, key=lambda x: x.get('composite_score', float('inf')))

            for eval_result in sorted_evals:
                model_name = eval_result.get('model_name', eval_result.get('model_key', 'Unknown'))
                mae = eval_result.get('mae', 0)
                r2 = eval_result.get('r2', 0)
                rmse = eval_result.get('rmse', 0)
                composite = eval_result.get('composite_score', 0)
                print(f"{model_name:<15} {mae:<10.3f} {r2:<10.3f} {rmse:<10.3f} {composite:<12.3f}")

            print("-" * 70)
            if not sorted_evals:
                print("No successful models to evaluate.")
            elif len(successful_models) > 1:
                best_model = sorted_evals[0]
                print(f"Best performing model: {best_model.get('model_name', best_model.get('model_key'))}")
            else:
                print(f"Single successful model: {sorted_evals[0].get('model_name', sorted_evals[0].get('model_key'))}")

            # Enhanced output options - restructured for better multi-model support
            print("\n[Chart] Step 10: Select visualization and output options")
            if len(successful_models) == 1:
                print(f"\nSingle model available: {successful_models[0]}")
                print("1. Save results to files (with standard plots)")
                print("2. Enhanced visualization analysis (no file output)")
                print("3. Detailed single model analysis")
                print("4. Quality control analysis (cross-plots and statistics)")
            else:
                print(f"\nMultiple models available: {', '.join(successful_models)}")
                print("1. Save results to files (select specific model)")
                print("2. Enhanced visualization analysis (select specific model)")
                print("3. Comprehensive multi-model comparison and ranking")
                print("4. Quality control analysis (select specific model)")
                print("5. Side-by-side model comparison plots")
                print("6. Generate comparison report for all models")

            output_handled = False
            while not output_handled:
                if len(successful_models) == 1:
                    choice = input("Enter choice (1, 2, 3, or 4): ").strip()
                else:
                    choice = input("Enter choice (1, 2, 3, 4, 5, or 6): ").strip()

                if choice == "1":
                    # Save results to files - need to select model if multiple available
                    if len(successful_models) == 1:
                        selected_output_model = successful_models[0]
                        selected_results = all_results[selected_output_model]
                        res_df = selected_results['res_df']
                        mres = selected_results['mres']
                    else:
                        print(f"\nSelect model for file output:")
                        selected_output_model = console_select(
                            successful_models,
                            "Choose model for file output",
                            default=successful_models[0]
                        )
                        selected_results = all_results[selected_output_model]
                        res_df = selected_results['res_df']
                        mres = selected_results['mres']

                    # Get output directory
                    print("\nSelect output directory...")
                    out = select_output_directory()
                    if not out:
                        print("Output directory selection cancelled. Skipping file output.")
                        continue

                    # Generate and save results
                    print(f"\nGenerating and saving results for {selected_output_model}...")
                    create_summary_plots(res_df, mres, cfg, model_name=selected_output_model, show_error_bands=True)
                    write_results_to_las(res_df, tgt, las_objs, out)
                    generate_final_report(mres)

                    print("\nResults processing completed!")
                    print(f"Results saved to: {out}")
                    output_handled = True

                elif choice == "2":
                    # Enhanced visualization analysis - need to select model if multiple available
                    if len(successful_models) == 1:
                        selected_output_model = successful_models[0]
                        selected_results = all_results[selected_output_model]
                        res_df = selected_results['res_df']
                        mres = selected_results['mres']
                    else:
                        print(f"\nSelect model for detailed visualization:")
                        selected_output_model = console_select(
                            successful_models,
                            "Choose model for visualization",
                            default=successful_models[0]
                        )
                        selected_results = all_results[selected_output_model]
                        res_df = selected_results['res_df']
                        mres = selected_results['mres']

                    print(f"\nGenerating enhanced visualization analysis for {selected_output_model}...")

                    # Separate comparison plots for better clarity
                    print("Creating separate Original vs Imputed and Original vs Predicted plots...")
                    create_separate_comparison_plots(res_df, mres, cfg, model_name=selected_output_model)

                    # Cross-plot analysis for quality control
                    print("Creating cross-plot analysis for quality control...")
                    create_crossplot_analysis(res_df, mres, cfg, model_name=selected_output_model, color_by='well')

                    print("\nEnhanced visualization analysis completed!")
                    print("Separate plots and cross-plot analysis generated for detailed review")
                    output_handled = True

                elif choice == "3":
                    # Comprehensive model comparison and ranking
                    if len(successful_models) == 1:
                        # Single model detailed analysis
                        selected_output_model = successful_models[0]
                        selected_results = all_results[selected_output_model]
                        res_df = selected_results['res_df']
                        mres = selected_results['mres']

                        print(f"\nDetailed analysis for single model: {selected_output_model}")
                        create_separate_comparison_plots(res_df, mres, cfg, model_name=selected_output_model)
                        create_crossplot_analysis(res_df, mres, cfg, model_name=selected_output_model, color_by='well')
                    else:
                        # Multi-model comparison and ranking
                        print("\nGenerating comprehensive multi-model comparison and ranking...")

                        # Model ranking visualization
                        print("Creating model ranking visualization...")
                        create_model_ranking_visualization(all_results, tgt, combined_evaluations)

                        # Individual model plots
                        print("Creating individual model plots...")
                        for model_key in successful_models:
                            model_results = all_results[model_key]
                            print(f"   • Generating plots for {model_key}...")
                            create_separate_comparison_plots(model_results['res_df'], model_results['mres'], cfg,
                                                           model_name=model_key)

                        # Multi-model comparison plot
                        print("Creating multi-model comparison plot...")
                        create_multi_model_comparison_plots(all_results, cfg, tgt)

                        # Combined performance report
                        print("Generating combined performance report...")
                        combined_mres = {
                            'target': tgt,
                            'evaluations': combined_evaluations,
                            'best_model_name': sorted(combined_evaluations, key=lambda x: x.get('composite_score', float('inf')))[0].get('model_name', 'Unknown')
                        }
                        generate_final_report(combined_mres)

                    print("\nComprehensive model comparison and ranking completed!")
                    print("All comparison plots, rankings, and analysis generated for review")
                    output_handled = True

                elif choice == "4":
                    # Quality control analysis - need to select model if multiple available
                    if len(successful_models) == 1:
                        selected_output_model = successful_models[0]
                        selected_results = all_results[selected_output_model]
                        res_df = selected_results['res_df']
                        mres = selected_results['mres']

                        print(f"\n[Chart] Generating quality control analysis for {selected_output_model}...")
                        create_crossplot_analysis(res_df, mres, cfg, model_name=selected_output_model, color_by='depth')
                        create_separate_comparison_plots(res_df, mres, cfg, model_name=selected_output_model)
                    else:
                        print(f"\nSelect model for quality control analysis:")
                        selected_output_model = console_select(
                            successful_models,
                            "Choose model for QC analysis",
                            default=successful_models[0]
                        )
                        selected_results = all_results[selected_output_model]
                        res_df = selected_results['res_df']
                        mres = selected_results['mres']

                        print(f"\nGenerating quality control analysis for {selected_output_model}...")
                        create_crossplot_analysis(res_df, mres, cfg, model_name=selected_output_model, color_by='depth')
                        create_separate_comparison_plots(res_df, mres, cfg, model_name=selected_output_model)

                    print("\nQuality control analysis completed!")
                    print("Cross-plots, statistics, and quality metrics generated for review")
                    output_handled = True

                elif choice == "5" and len(successful_models) > 1:
                    # Side-by-side model comparison plots
                    print("\nGenerating side-by-side model comparison plots...")

                    # Multi-model comparison plot
                    print("Creating side-by-side comparison plots...")
                    create_multi_model_comparison_plots(all_results, cfg, tgt)

                    print("\nSide-by-side comparison plots completed!")
                    print("Visual comparison of all models generated for review")
                    output_handled = True

                elif choice == "6" and len(successful_models) > 1:
                    # Generate comparison report for all models
                    print("\nGenerating comprehensive comparison report for all models...")

                    # Combined performance report
                    print("Generating detailed performance report...")
                    combined_mres = {
                        'target': tgt,
                        'evaluations': combined_evaluations,
                        'best_model_name': sorted(combined_evaluations, key=lambda x: x.get('composite_score', float('inf')))[0].get('model_name', 'Unknown')
                    }
                    generate_final_report(combined_mres)

                    # Model ranking visualization
                    print("Creating comprehensive model ranking...")
                    create_model_ranking_visualization(all_results, tgt, combined_evaluations)

                    print("\nComprehensive comparison report completed!")
                    print("Detailed performance analysis and ranking generated for all models")
                    output_handled = True

                else:
                    if len(successful_models) == 1:
                        print("Invalid choice. Please enter 1, 2, 3, or 4.")
                    else:
                        print("Invalid choice. Please enter 1, 2, 3, 4, 5, or 6.")

        # Step 11: Continue or Exit menu
        print("\n🔄 Step 11: Next action")
        print("What would you like to do next?")
        print("1. Exit the program")
        print("2. Continue processing (run another model)")
        print("3. Go back to plot options (Step 9)")

        step_10_handled = False
        while not step_10_handled:
            choice = input("Enter choice (1, 2, or 3): ").strip()
            if choice == "1":
                print("\n👋 Exiting program. Thank you for using ML Log Prediction!")
                return
            elif choice == "2":
                print("\n🔄 Continuing to model selection...")
                step_10_handled = True
                break  # Break out of Step 10 menu to continue the main loop
            elif choice == "3":
                print("\n🔙 Going back to plot options...")
                # Go back to Step 10 plot options
                if not successful_models:
                    print("⚠️ No successful models to process. Cannot return to plot options.")
                    continue

                # Re-run the Step 10 output options
                print("\n💾 Step 10: Configure output options (Revisited)")

                # Display model performance summary (same as original Step 9)
                print("\nModel Performance Summary:")
                print("-" * 70)
                print(f"{'Model':<15} {'MAE':<10} {'R²':<10} {'RMSE':<10} {'Composite':<12}")
                print("-" * 70)

                # Sort evaluations by composite score for ranking
                sorted_evals = sorted(combined_evaluations, key=lambda x: x.get('composite_score', float('inf')))

                for eval_result in sorted_evals:
                    model_name = eval_result.get('model_name', eval_result.get('model_key', 'Unknown'))
                    mae = eval_result.get('mae', 0)
                    r2 = eval_result.get('r2', 0)
                    rmse = eval_result.get('rmse', 0)
                    composite = eval_result.get('composite_score', 0)
                    print(f"{model_name:<15} {mae:<10.3f} {r2:<10.3f} {rmse:<10.3f} {composite:<12.3f}")

                print("-" * 70)
                if len(successful_models) > 1:
                    best_model = sorted_evals[0]
                    print(f"Best performing model: {best_model.get('model_name', best_model.get('model_key'))}")
                else:
                    print(f"Single successful model: {sorted_evals[0].get('model_name', sorted_evals[0].get('model_key'))}")

                # Re-establish model selection for single model scenarios
                if len(successful_models) == 1:
                    selected_output_model = successful_models[0]
                    selected_results = all_results[selected_output_model]
                    res_df = selected_results['res_df']
                    mres = selected_results['mres']

                # Enhanced output options - exactly matching original Step 10
                print("\n[Chart] Step 10: Select visualization and output options")
                if len(successful_models) == 1:
                    print(f"\nSingle model available: {successful_models[0]}")
                    print("1. Save results to files (with standard plots)")
                    print("2. Enhanced visualization analysis (no file output)")
                    print("3. Detailed single model analysis")
                    print("4. Quality control analysis (cross-plots and statistics)")
                else:
                    print(f"\nMultiple models available: {', '.join(successful_models)}")
                    print("1. Save results to files (select specific model)")
                    print("2. Enhanced visualization analysis (select specific model)")
                    print("3. Comprehensive multi-model comparison and ranking")
                    print("4. Quality control analysis (select specific model)")
                    print("5. Side-by-side model comparison plots")
                    print("6. Generate comparison report for all models")

                output_handled = False
                while not output_handled:
                    if len(successful_models) == 1:
                        plot_choice = input("Enter choice (1, 2, 3, or 4): ").strip()
                    else:
                        plot_choice = input("Enter choice (1, 2, 3, 4, 5, or 6): ").strip()
                    if plot_choice == "1":
                        # Save results to files - need to select model if multiple available
                        if len(successful_models) > 1:
                            print(f"\nSelect model for file output:")
                            selected_output_model = console_select(
                                successful_models,
                                "Choose model for file output",
                                default=successful_models[0]
                            )
                            selected_results = all_results[selected_output_model]
                            res_df = selected_results['res_df']
                            mres = selected_results['mres']

                        # Get output directory
                        print("\n📁 Select output directory...")
                        out = select_output_directory()
                        if not out:
                            print("❌ Output directory selection cancelled. Skipping file output.")
                            continue

                        # Generate and save results
                        print(f"\n📈 Generating and saving results for {selected_output_model}...")
                        create_summary_plots(res_df, mres, cfg, model_name=selected_output_model, show_error_bands=True)
                        write_results_to_las(res_df, tgt, las_objs, out)
                        generate_final_report(mres)

                        print("\n🎉 Results processing completed!")
                        print(f"📁 Results saved to: {out}")
                        output_handled = True

                    elif plot_choice == "2":
                        # Enhanced visualization analysis - need to select model if multiple available
                        if len(successful_models) > 1:
                            print(f"\nSelect model for detailed visualization:")
                            selected_output_model = console_select(
                                successful_models,
                                "Choose model for visualization",
                                default=successful_models[0]
                            )
                            selected_results = all_results[selected_output_model]
                            res_df = selected_results['res_df']
                            mres = selected_results['mres']

                        print(f"\n📈 Generating enhanced visualization analysis for {selected_output_model}...")

                        # Separate comparison plots for better clarity
                        print("📊 Creating separate Original vs Imputed and Original vs Predicted plots...")
                        create_separate_comparison_plots(res_df, mres, cfg, model_name=selected_output_model)

                        # Cross-plot analysis for quality control
                        print("📊 Creating cross-plot analysis for quality control...")
                        create_crossplot_analysis(res_df, mres, cfg, model_name=selected_output_model, color_by='well')

                        print("\n🎉 Enhanced visualization analysis completed!")
                        print("📊 Separate plots and cross-plot analysis generated for detailed review")
                        output_handled = True

                    elif plot_choice == "3":
                        if len(successful_models) == 1:
                            # Detailed single model analysis (option 3 for single model)
                            print(f"\nDetailed analysis for single model: {selected_output_model}")
                            create_separate_comparison_plots(res_df, mres, cfg, model_name=selected_output_model)
                            create_crossplot_analysis(res_df, mres, cfg, model_name=selected_output_model, color_by='well')
                        else:
                            # Comprehensive multi-model comparison and ranking (option 3 for multiple models)
                            print("\nGenerating comprehensive multi-model comparison and ranking...")

                            # Model ranking visualization
                            print("Creating model ranking visualization...")
                            create_model_ranking_visualization(all_results, tgt, combined_evaluations)

                            # Individual model plots
                            print("Creating individual model plots...")
                            for model_key in successful_models:
                                model_results = all_results[model_key]
                                print(f"   • Generating plots for {model_key}...")
                                create_separate_comparison_plots(model_results['res_df'], model_results['mres'], cfg,
                                                               model_name=model_key)

                            # Multi-model comparison plot
                            print("Creating multi-model comparison plot...")
                            create_multi_model_comparison_plots(all_results, cfg, tgt)

                            # Combined performance report
                            print("Generating combined performance report...")
                            combined_mres = {
                                'target': tgt,
                                'evaluations': combined_evaluations,
                                'best_model_name': sorted(combined_evaluations, key=lambda x: x.get('composite_score', float('inf')))[0].get('model_name', 'Unknown')
                            }
                            generate_final_report(combined_mres)

                        print("\nComprehensive model comparison and ranking completed!")
                        print("All comparison plots, rankings, and analysis generated for review")
                        output_handled = True

                    elif plot_choice == "4":
                        # Quality control analysis - need to select model if multiple available
                        if len(successful_models) == 1:
                            print(f"\n[Chart] Generating quality control analysis for {selected_output_model}...")
                            create_crossplot_analysis(res_df, mres, cfg, model_name=selected_output_model, color_by='depth')
                            create_separate_comparison_plots(res_df, mres, cfg, model_name=selected_output_model)
                        else:
                            print(f"\nSelect model for quality control analysis:")
                            selected_output_model = console_select(
                                successful_models,
                                "Choose model for QC analysis",
                                default=successful_models[0]
                            )
                            selected_results = all_results[selected_output_model]
                            res_df = selected_results['res_df']
                            mres = selected_results['mres']

                            print(f"\nGenerating quality control analysis for {selected_output_model}...")
                            create_crossplot_analysis(res_df, mres, cfg, model_name=selected_output_model, color_by='depth')
                            create_separate_comparison_plots(res_df, mres, cfg, model_name=selected_output_model)

                        print("\nQuality control analysis completed!")
                        print("Cross-plots, statistics, and quality metrics generated for review")
                        output_handled = True

                    elif plot_choice == "5" and len(successful_models) > 1:
                        # Side-by-side model comparison plots (option 5 for multiple models)
                        print("\nGenerating side-by-side model comparison plots...")

                        # Multi-model comparison plot
                        print("Creating side-by-side comparison plots...")
                        create_multi_model_comparison_plots(all_results, cfg, tgt)

                        print("\nSide-by-side comparison plots completed!")
                        print("Visual comparison of all models generated for review")
                        output_handled = True

                    elif plot_choice == "6" and len(successful_models) > 1:
                        # Generate comparison report for all models (option 6 for multiple models)
                        print("\nGenerating comprehensive comparison report for all models...")

                        # Combined performance report
                        print("Generating detailed performance report...")
                        combined_mres = {
                            'target': tgt,
                            'evaluations': combined_evaluations,
                            'best_model_name': sorted(combined_evaluations, key=lambda x: x.get('composite_score', float('inf')))[0].get('model_name', 'Unknown')
                        }
                        generate_final_report(combined_mres)

                        # Model ranking visualization
                        print("Creating comprehensive model ranking...")
                        create_model_ranking_visualization(all_results, tgt, combined_evaluations)

                        print("\nComprehensive comparison report completed!")
                        print("Detailed performance analysis and ranking generated for all models")
                        output_handled = True

                    else:
                        if len(successful_models) == 1:
                            print("Invalid choice. Please enter 1, 2, 3, or 4.")
                        else:
                            print("Invalid choice. Please enter 1, 2, 3, 4, 5, or 6.")
                
                # After completing Step 10 again, continue to Step 11
                continue
            else:
                print("Invalid choice. Please enter 1, 2, or 3.")

        # Continue to next iteration of main loop (back to Step 9)
    
    # Phase 1: Final memory status report
    if MEMORY_OPTIMIZATION_AVAILABLE and memory_optimizer:
        print("\n" + "="*60)
        print("[MEM] FINAL MEMORY STATUS REPORT")
        print("="*60)
        memory_optimizer.print_memory_status()
        print("[SUCCESS] Phase 1 memory optimization completed successfully")

if __name__ == '__main__':
    main()
