# ML Models Implementation Guide

## Quick Fix Implementation

### 1. TensorFlow DLL Loading Fix

#### Step 1: Environment Setup
```bash
# Create backup of current environment
conda env export > environment_backup.yml

# Set environment variables (add to your .env or system environment)
set TF_ENABLE_ONEDNN_OPTS=0
set CUDA_VISIBLE_DEVICES=-1
set TF_CPP_MIN_LOG_LEVEL=2
```

#### Step 2: Dependency Reinstallation
```bash
# Uninstall problematic packages
pip uninstall tensorflow tensorflow-gpu tensorflow-cpu -y
pip uninstall numpy -y

# Install compatible versions
pip install numpy==1.23.5
pip install tensorflow-cpu==2.13.0
pip install pypots==0.2.0

# Verify installation
python -c "import tensorflow as tf; print(tf.__version__)"
python -c "import pypots; print('PyPOTS available')"
```

#### Step 3: Run Automated Fix
```bash
# Execute the existing fix script
python fix_tensorflow_dll.py
```

### 2. Data Shape Validation Fix

#### Modify `enhanced_preprocessing.py`

**Location:** Line 366 in `introduce_realistic_missingness` method

```python
# BEFORE (current problematic code)
def introduce_realistic_missingness(self, sequences):
    if len(sequences.shape) != 3:
        raise ValueError("Input sequences must be 3D array (n_sequences, seq_len, n_features)")

# AFTER (fixed code)
def introduce_realistic_missingness(self, sequences):
    # Convert to numpy array if not already
    if not isinstance(sequences, np.ndarray):
        sequences = np.array(sequences)
    
    # Handle 2D to 3D conversion
    if len(sequences.shape) == 2:
        logger.info(f"Converting 2D sequences {sequences.shape} to 3D")
        sequences = sequences.reshape(1, sequences.shape[0], sequences.shape[1])
    
    # Validate 3D shape
    if len(sequences.shape) != 3:
        raise ValueError(
            f"Input sequences must be 3D array (n_sequences, seq_len, n_features), "
            f"got shape: {sequences.shape}"
        )
    
    logger.debug(f"Processing sequences with shape: {sequences.shape}")
    # Continue with existing logic...
```

#### Modify `data_handler.py`

**Location:** `introduce_missingness` function around line 313

```python
# BEFORE
def introduce_missingness(sequences, missing_rate=0.1, random_seed=42, target_col_name=None, enhanced=True):
    if enhanced:
        try:
            result = enhanced_introduce_missingness(sequences, missing_rate, random_seed)
            return result
        except Exception as e:
            logger.warning(f"Enhanced missingness failed: {e}. Falling back to standard method.")

# AFTER
def introduce_missingness(sequences, missing_rate=0.1, random_seed=42, target_col_name=None, enhanced=True):
    # Ensure proper shape before processing
    if isinstance(sequences, np.ndarray):
        logger.debug(f"Input sequences shape: {sequences.shape}")
        
        # Convert 2D to 3D if necessary
        if len(sequences.shape) == 2:
            logger.info(f"Converting 2D input {sequences.shape} to 3D for processing")
            sequences = sequences.reshape(1, sequences.shape[0], sequences.shape[1])
    
    if enhanced:
        try:
            result = enhanced_introduce_missingness(sequences, missing_rate, random_seed)
            return result
        except Exception as e:
            logger.warning(f"Enhanced missingness failed: {e}. Falling back to standard method.")
            # Continue with standard method...
```

### 3. PyPOTS Compatibility Enhancement

#### Create Enhanced Compatibility Check

**Create new file:** `utils/enhanced_compatibility.py`

```python
import logging
import sys
import importlib
from typing import Tuple, Optional

logger = logging.getLogger(__name__)

def check_tensorflow_comprehensive() -> Tuple[bool, Optional[str]]:
    """Comprehensive TensorFlow availability check."""
    try:
        import tensorflow as tf
        # Test basic functionality
        _ = tf.constant([1, 2, 3])
        return True, tf.__version__
    except ImportError as e:
        if "DLL load failed" in str(e):
            return False, "DLL_LOAD_FAILED"
        return False, f"IMPORT_ERROR: {str(e)}"
    except Exception as e:
        return False, f"RUNTIME_ERROR: {str(e)}"

def check_pypots_comprehensive() -> Tuple[bool, Optional[str]]:
    """Comprehensive PyPOTS availability check."""
    tf_available, tf_error = check_tensorflow_comprehensive()
    
    if not tf_available:
        return False, f"TensorFlow dependency failed: {tf_error}"
    
    try:
        import pypots
        return True, pypots.__version__
    except ImportError as e:
        return False, f"PyPOTS import failed: {str(e)}"
    except Exception as e:
        return False, f"PyPOTS runtime error: {str(e)}"

def get_system_diagnostics() -> dict:
    """Get comprehensive system diagnostics."""
    diagnostics = {
        'python_version': sys.version,
        'platform': sys.platform,
        'tensorflow': check_tensorflow_comprehensive(),
        'pypots': check_pypots_comprehensive()
    }
    
    # Check additional dependencies
    for package in ['numpy', 'torch', 'sklearn']:
        try:
            module = importlib.import_module(package)
            diagnostics[package] = (True, getattr(module, '__version__', 'unknown'))
        except ImportError:
            diagnostics[package] = (False, 'Not installed')
    
    return diagnostics
```

#### Update Model Initialization

**Modify:** `ml_core_phase1_integration.py`

```python
# Add at the top of the file
from utils.enhanced_compatibility import check_pypots_comprehensive, get_system_diagnostics

# Modify model creation logic
def create_model_with_diagnostics(model_name, **kwargs):
    """Create model with comprehensive diagnostics."""
    
    if model_name.lower() in ['brits', 'saits']:
        pypots_available, pypots_error = check_pypots_comprehensive()
        
        if not pypots_available:
            logger.error(f"PyPOTS not available for {model_name}: {pypots_error}")
            
            # Print system diagnostics
            diagnostics = get_system_diagnostics()
            logger.info("System Diagnostics:")
            for key, (available, version) in diagnostics.items():
                status = "✅" if available else "❌"
                logger.info(f"  {status} {key}: {version}")
            
            # Suggest fixes
            if "DLL_LOAD_FAILED" in str(pypots_error):
                logger.info("💡 Suggested fixes:")
                logger.info("   1. Run: python fix_tensorflow_dll.py")
                logger.info("   2. Install Visual C++ Redistributable")
                logger.info("   3. Use: pip install tensorflow-cpu==2.13.0")
            
            return None, f"PyPOTS dependency failed: {pypots_error}"
    
    # Continue with normal model creation...
```

## 4. Testing and Validation

### Create Test Script

**Create:** `test_ml_fixes.py`

```python
import numpy as np
import logging
from utils.enhanced_compatibility import get_system_diagnostics
from preprocessing.deep_model.enhanced_preprocessing import EnhancedLogPreprocessor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_system_compatibility():
    """Test system compatibility."""
    logger.info("=== System Compatibility Test ===")
    
    diagnostics = get_system_diagnostics()
    all_passed = True
    
    for component, (available, version) in diagnostics.items():
        status = "✅ PASS" if available else "❌ FAIL"
        logger.info(f"{status} {component}: {version}")
        if not available and component in ['tensorflow', 'pypots']:
            all_passed = False
    
    return all_passed

def test_data_shape_handling():
    """Test data shape handling fixes."""
    logger.info("=== Data Shape Handling Test ===")
    
    # Test 2D input
    test_data_2d = np.random.rand(64, 6)  # (seq_len, n_features)
    logger.info(f"Testing 2D input: {test_data_2d.shape}")
    
    try:
        preprocessor = EnhancedLogPreprocessor()
        result = preprocessor.introduce_realistic_missingness(test_data_2d)
        logger.info("✅ 2D input handling: PASS")
        return True
    except Exception as e:
        logger.error(f"❌ 2D input handling: FAIL - {e}")
        return False

def test_model_creation():
    """Test model creation with new diagnostics."""
    logger.info("=== Model Creation Test ===")
    
    try:
        from ml_core_phase1_integration import create_model_with_diagnostics
        
        # Test BRITS model creation
        model, error = create_model_with_diagnostics(
            'brits',
            sequence_len=64,
            n_features=6,
            rnn_hidden_size=128
        )
        
        if model is not None:
            logger.info("✅ BRITS model creation: PASS")
            return True
        else:
            logger.warning(f"⚠️ BRITS model creation: SKIP - {error}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Model creation test: FAIL - {e}")
        return False

if __name__ == "__main__":
    logger.info("Starting ML Fixes Validation...")
    
    tests = [
        test_system_compatibility,
        test_data_shape_handling,
        test_model_creation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            logger.error(f"Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    
    logger.info(f"\n=== Test Summary ===")
    logger.info(f"Passed: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 All tests passed! System is ready.")
    else:
        logger.warning(f"⚠️ {total - passed} tests failed. Review fixes needed.")
```

## 5. Deployment Checklist

### Pre-deployment
- [ ] Backup current environment
- [ ] Run `test_ml_fixes.py`
- [ ] Verify TensorFlow installation
- [ ] Check PyPOTS availability

### Deployment Steps
1. Apply TensorFlow DLL fixes
2. Update enhanced preprocessing
3. Deploy compatibility enhancements
4. Run validation tests
5. Monitor system performance

### Post-deployment
- [ ] Monitor error logs
- [ ] Validate model performance
- [ ] Check memory usage
- [ ] Verify fallback mechanisms

## 6. Troubleshooting Guide

### Common Issues

**Issue:** TensorFlow DLL still failing
**Solution:** 
```bash
# Complete environment reset
conda create -n ml_fixed python=3.9
conda activate ml_fixed
pip install -r requirements_fixed.txt
```

**Issue:** Data shape errors persist
**Solution:** Add debug logging to trace data flow
```python
logger.debug(f"Data shape at each step: {data.shape}")
```

**Issue:** PyPOTS import fails
**Solution:** Use alternative model implementation
```python
# Implement native PyTorch BRITS as fallback
if not pypots_available:
    model = BRITSNative(**kwargs)
```

This implementation guide provides concrete steps to resolve all identified issues while maintaining system stability and performance.